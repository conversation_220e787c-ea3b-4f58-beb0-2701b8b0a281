from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import List, Optional
from datetime import datetime, timedelta
from app.schemas.contract_lifecycle import (
    ContractVersion, ContractVersionCreate,
    ContractRenewal, ContractRenewalCreate, ContractRenewalUpdate,
    ContractLifecycleEvent, ContractLifecycleEventCreate,
    ContractReminder, ContractReminderCreate, ContractReminderUpdate,
    VersionComparison, ContractLifecycleStats, ExpirationAlert,
    BulkRenewalRequest, BulkRenewalResponse, StatusTransitionRequest
)
from app.core.auth import get_current_user, validate_workspace_access, get_authenticated_db_client
from app.services.contract_lifecycle_service import ContractLifecycleService
from app.utils.response import handle_supabase_response, create_success_response, not_found_error
import uuid

router = APIRouter()

# Version Management Endpoints

@router.get("/contracts/{contract_id}/versions", response_model=List[ContractVersion])
async def get_contract_versions(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """Get all versions of a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.get_contract_versions(contract_id)

@router.get("/contracts/{contract_id}/versions/current", response_model=ContractVersion)
async def get_current_version(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """Get the current version of a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)
    
    version = await lifecycle_service.get_current_version(contract_id)
    if not version:
        raise not_found_error("Current version")
    
    return version

@router.post("/contracts/{contract_id}/versions", response_model=ContractVersion)
async def create_contract_version(
    contract_id: str = Path(..., description="The ID of the contract"),
    version_data: ContractVersionCreate = None,
    current_user: dict = Depends(get_current_user)
):
    """Create a new version of a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Get contract data to create version
    contract_response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    contract = contract_data[0]
    await validate_workspace_access(current_user["id"], contract["workspace_id"], supabase)
    
    # If no version data provided, create from current contract state
    if not version_data:
        version_data = ContractVersionCreate(
            contract_id=contract_id,
            version_number=1,  # Will be auto-incremented by service
            title=contract["title"],
            content=contract,
            changes_summary="Manual version creation",
            workspace_id=contract["workspace_id"]
        )
    
    return await lifecycle_service.create_version(version_data, current_user["id"])

@router.get("/versions/{version1_id}/compare/{version2_id}", response_model=VersionComparison)
async def compare_versions(
    version1_id: str = Path(..., description="The ID of the first version"),
    version2_id: str = Path(..., description="The ID of the second version"),
    current_user: dict = Depends(get_current_user)
):
    """Compare two versions of a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify both versions exist and user has access
    for version_id in [version1_id, version2_id]:
        version_response = supabase.table("contract_versions").select("workspace_id").eq("id", version_id).execute()
        version_data = handle_supabase_response(version_response, "Failed to fetch version")
        
        if not version_data:
            raise not_found_error("Version")
        
        await validate_workspace_access(current_user["id"], version_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.compare_versions(version1_id, version2_id)

# Renewal Management Endpoints

@router.get("/contracts/{contract_id}/renewals", response_model=List[ContractRenewal])
async def get_contract_renewals(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """Get all renewals for a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.get_contract_renewals(contract_id)

@router.post("/contracts/{contract_id}/renewals", response_model=ContractRenewal)
async def create_contract_renewal(
    contract_id: str = Path(..., description="The ID of the contract"),
    renewal_data: ContractRenewalCreate = None,
    current_user: dict = Depends(get_current_user)
):
    """Create a new contract renewal request."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    contract = contract_data[0]
    await validate_workspace_access(current_user["id"], contract["workspace_id"], supabase)
    
    # If no renewal data provided, create default renewal
    if not renewal_data:
        if not contract.get("expiry_date"):
            raise HTTPException(status_code=400, detail="Contract has no expiry date")
        
        original_expiry = datetime.fromisoformat(contract["expiry_date"].replace('Z', '+00:00'))
        new_expiry = original_expiry + timedelta(days=365)  # Default 1 year renewal
        
        renewal_data = ContractRenewalCreate(
            contract_id=contract_id,
            original_expiry_date=original_expiry,
            new_expiry_date=new_expiry,
            renewal_period="1 year",
            workspace_id=contract["workspace_id"]
        )
    
    return await lifecycle_service.create_renewal(renewal_data, current_user["id"])

@router.put("/renewals/{renewal_id}/approve", response_model=ContractRenewal)
async def approve_renewal(
    renewal_id: str = Path(..., description="The ID of the renewal"),
    current_user: dict = Depends(get_current_user)
):
    """Approve a contract renewal."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify renewal exists and user has access
    renewal_response = supabase.table("contract_renewals").select("workspace_id").eq("id", renewal_id).execute()
    renewal_data = handle_supabase_response(renewal_response, "Failed to fetch renewal")
    
    if not renewal_data:
        raise not_found_error("Renewal")
    
    await validate_workspace_access(current_user["id"], renewal_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.approve_renewal(renewal_id, current_user["id"])

@router.put("/renewals/{renewal_id}/complete", response_model=ContractRenewal)
async def complete_renewal(
    renewal_id: str = Path(..., description="The ID of the renewal"),
    current_user: dict = Depends(get_current_user)
):
    """Complete a contract renewal and update the contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify renewal exists and user has access
    renewal_response = supabase.table("contract_renewals").select("workspace_id").eq("id", renewal_id).execute()
    renewal_data = handle_supabase_response(renewal_response, "Failed to fetch renewal")
    
    if not renewal_data:
        raise not_found_error("Renewal")
    
    await validate_workspace_access(current_user["id"], renewal_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.complete_renewal(renewal_id)

# Lifecycle Events Endpoints

@router.get("/contracts/{contract_id}/events", response_model=List[ContractLifecycleEvent])
async def get_lifecycle_events(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """Get all lifecycle events for a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)
    
    return await lifecycle_service.get_lifecycle_events(contract_id)

@router.post("/contracts/{contract_id}/events", response_model=ContractLifecycleEvent)
async def create_lifecycle_event(
    contract_id: str = Path(..., description="The ID of the contract"),
    event_data: ContractLifecycleEventCreate = None,
    current_user: dict = Depends(get_current_user)
):
    """Create a new lifecycle event for a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)
    
    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")
    
    if not contract_data:
        raise not_found_error("Contract")
    
    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)
    
    # Set workspace_id and triggered_by if not provided
    if not event_data.workspace_id:
        event_data.workspace_id = contract_data[0]["workspace_id"]
    if not event_data.triggered_by:
        event_data.triggered_by = current_user["id"]
    
    return await lifecycle_service.create_lifecycle_event(event_data)

# Reminder Management Endpoints

@router.get("/contracts/{contract_id}/reminders", response_model=List[ContractReminder])
async def get_contract_reminders(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """Get all reminders for a contract."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")

    if not contract_data:
        raise not_found_error("Contract")

    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)

    return await lifecycle_service.get_contract_reminders(contract_id)

@router.get("/workspaces/{workspace_id}/reminders/pending", response_model=List[ContractReminder])
async def get_pending_reminders(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    current_user: dict = Depends(get_current_user)
):
    """Get all pending reminders for a workspace."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return await lifecycle_service.get_pending_reminders(workspace_id)

@router.put("/reminders/{reminder_id}/mark-sent", response_model=ContractReminder)
async def mark_reminder_sent(
    reminder_id: str = Path(..., description="The ID of the reminder"),
    current_user: dict = Depends(get_current_user)
):
    """Mark a reminder as sent."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    # Verify reminder exists and user has access
    reminder_response = supabase.table("contract_reminders").select("workspace_id").eq("id", reminder_id).execute()
    reminder_data = handle_supabase_response(reminder_response, "Failed to fetch reminder")

    if not reminder_data:
        raise not_found_error("Reminder")

    await validate_workspace_access(current_user["id"], reminder_data[0]["workspace_id"], supabase)

    return await lifecycle_service.mark_reminder_sent(reminder_id)

# Analytics and Reporting Endpoints

@router.get("/workspaces/{workspace_id}/lifecycle/stats", response_model=ContractLifecycleStats)
async def get_lifecycle_stats(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    current_user: dict = Depends(get_current_user)
):
    """Get lifecycle statistics for a workspace."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return await lifecycle_service.get_lifecycle_stats(workspace_id)

@router.get("/workspaces/{workspace_id}/expiration-alerts", response_model=List[ExpirationAlert])
async def get_expiration_alerts(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    days_ahead: int = Query(30, description="Number of days ahead to check for expirations"),
    current_user: dict = Depends(get_current_user)
):
    """Get contracts expiring within the specified number of days."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return await lifecycle_service.get_expiration_alerts(workspace_id, days_ahead)

# Bulk Operations Endpoints

@router.post("/workspaces/{workspace_id}/bulk-renewal", response_model=BulkRenewalResponse)
async def bulk_renewal(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    bulk_request: BulkRenewalRequest = None,
    current_user: dict = Depends(get_current_user)
):
    """Process bulk renewal requests."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return await lifecycle_service.bulk_renewal(bulk_request, current_user["id"], workspace_id)

# Status Management Endpoints

@router.put("/contracts/{contract_id}/status", response_model=dict)
async def transition_contract_status(
    contract_id: str = Path(..., description="The ID of the contract"),
    status_request: StatusTransitionRequest = None,
    current_user: dict = Depends(get_current_user)
):
    """Transition a contract to a new status."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    # Verify contract exists and user has access
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(contract_response, "Failed to fetch contract")

    if not contract_data:
        raise not_found_error("Contract")

    await validate_workspace_access(current_user["id"], contract_data[0]["workspace_id"], supabase)

    # Set contract_id if not provided
    if not status_request.contract_id:
        status_request.contract_id = contract_id

    return await lifecycle_service.transition_contract_status(status_request, current_user["id"])

# Automated Operations Endpoints

@router.post("/workspaces/{workspace_id}/update-expired", response_model=dict)
async def update_expired_contracts(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    current_user: dict = Depends(get_current_user)
):
    """Update contracts that have passed their expiry date to expired status."""
    supabase = get_authenticated_db_client(current_user)
    lifecycle_service = ContractLifecycleService(supabase)

    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return await lifecycle_service.update_expired_contracts(workspace_id)
