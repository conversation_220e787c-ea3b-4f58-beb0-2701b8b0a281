from fastapi import APIRouter, Depends, HTTPException, Query, Path, Response
from fastapi.responses import FileResponse
from typing import List, Optional
import uuid
from datetime import datetime, timedelta
from app.schemas.contract import Contract, ContractCreate, ContractUpdate
from app.core.auth import get_current_user, validate_workspace_access, get_authenticated_db_client
from app.db.database import get_supabase_client
from app.services.document_generator import DocumentGeneratorService
from app.services.ai_service import ai_service
from app.schemas.ai_analysis import AIAnalysisResponse, AIAnalysisResult as AIAnalysisResultSchema
from app.utils.response import (
    handle_supabase_response,
    create_success_response,
    create_created_response,
    not_found_error,
    forbidden_error
)
from datetime import datetime
import uuid
import tempfile
import os
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[Contract])
async def get_contracts(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    status: Optional[str] = Query(None, description="Filter by contract status"),
    type: Optional[str] = Query(None, description="Filter by contract type"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve contracts for a specific workspace that the user has access to.
    RLS automatically filters results to user's accessible workspaces.
    """
    # Use authenticated client with RLS - automatically filters by user's workspace access
    supabase = get_authenticated_db_client(current_user)

    # With RLS enabled, we still validate workspace access for explicit error handling
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Start building the query - RLS will automatically filter by workspace access
    query = supabase.table("contracts").select("*").eq("workspace_id", workspace_id)

    # Apply additional filters
    if status:
        query = query.eq("status", status)
    if type:
        query = query.eq("type", type)
    if search:
        query = query.or_(f"title.ilike.%{search}%,description.ilike.%{search}%")

    # Apply pagination
    query = query.range(skip, skip + limit - 1)

    # Execute the query
    response = query.execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to retrieve contracts")

    return data

@router.post("/", response_model=Contract)
async def create_contract(
    contract: ContractCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new contract in a workspace the user has access to.
    RLS automatically enforces workspace access on insert.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate that the user has access to the specified workspace
    workspace_id = contract.workspace_id
    if workspace_id:
        await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Prepare the contract data
    contract_data = contract.model_dump()
    contract_data["id"] = str(uuid.uuid4())
    contract_data["created_by"] = {
        "id": current_user["id"],
        "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip() or "User"
    }
    contract_data["created_at"] = datetime.utcnow().isoformat()

    # Insert the contract
    response = supabase.table("contracts").insert(contract_data).execute()

    # Handle contract creation response
    contract_data_result = handle_supabase_response(response, "Failed to create contract")

    if not contract_data_result:
        raise HTTPException(status_code=500, detail="Contract creation failed - no data returned")

    return contract_data_result[0]

@router.get("/{contract_id}", response_model=Contract)
async def get_contract(
    contract_id: str = Path(..., description="The ID of the contract to retrieve"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific contract by ID with workspace access validation.
    RLS automatically filters to only contracts in user's accessible workspaces.
    """
    # Use authenticated client with RLS - will only return contracts user has access to
    supabase = get_authenticated_db_client(current_user)

    # Get the contract - RLS will automatically filter by workspace access
    response = supabase.table("contracts").select("*").eq("id", contract_id).execute()

    # Handle contract retrieval response
    contract_data = handle_supabase_response(response, "Failed to retrieve contract")

    if not contract_data:
        raise not_found_error("Contract")

    contract = contract_data[0]
    workspace_id = contract.get("workspace_id")

    if workspace_id:
        # Validate that the user has access to this contract's workspace
        await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return contract

@router.post("/{contract_id}/generate-document")
async def generate_contract_document(
    contract_id: str = Path(..., description="The ID of the contract"),
    format_type: str = Query("pdf", description="Document format (pdf, docx, html, txt, markdown)"),
    template_name: Optional[str] = Query(None, description="Custom template name"),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate a professional document from contract data in various formats.
    """
    supabase = get_supabase_client()

    # Get contract data
    response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(response, "Failed to retrieve contract")

    if not contract_data:
        raise not_found_error("Contract")

    contract = contract_data[0]

    try:
        # Initialize document generator
        doc_generator = DocumentGeneratorService()

        # Get workspace branding settings
        workspace_response = supabase.table("workspaces").select("settings").eq("id", contract["workspace_id"]).execute()
        workspace_data = handle_supabase_response(workspace_response, "Failed to retrieve workspace")

        # Default branding options
        default_branding = {
            "company_name": "LegalAI",
            "letterhead": True,
            "footer_text": "Generated by LegalAI Advanced Document Generator",
            "color_scheme": {
                "primary": "#1e293b",
                "secondary": "#64748b",
                "accent": "#3b82f6"
            },
            "fonts": {
                "heading": "Arial, sans-serif",
                "body": "Times New Roman, serif"
            },
            "document_settings": {
                "include_watermark": False,
                "page_margins": "normal",
                "line_spacing": "1.5"
            }
        }

        # Use workspace branding if available
        branding_options = default_branding
        if workspace_data and workspace_data[0].get("settings", {}).get("branding"):
            workspace_branding = workspace_data[0]["settings"]["branding"]
            branding_options.update(workspace_branding)

        # Generate document
        file_info = await doc_generator.generate_contract_document(
            contract_data=contract,
            format_type=format_type,
            template_name=template_name,
            branding_options=branding_options
        )

        # Create export history record
        try:
            export_record = {
                "id": str(uuid.uuid4()),
                "contract_id": contract_id,
                "contract_title": contract.get("title", "Unknown Contract"),
                "format": format_type.upper(),
                "file_size": file_info.get("size", 0),
                "download_url": file_info.get("url", ""),
                "template_used": template_name,
                "branding_settings": branding_options,
                "workspace_id": contract["workspace_id"],
                "exported_by": {
                    "id": current_user["id"],
                    "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip() or "User"
                },
                "exported_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(days=30)).isoformat(),
                "download_count": 0,
                "status": "active",
                "created_at": datetime.utcnow().isoformat()
            }

            # Insert export history record
            supabase.table("export_history").insert(export_record).execute()
        except Exception as e:
            # Log the error but don't fail the export
            print(f"Failed to create export history record: {e}")

        return {
            "success": True,
            "message": f"Document generated successfully in {format_type.upper()} format",
            "file_info": file_info
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Document generation failed: {str(e)}")

@router.post("/batch-generate-documents")
async def batch_generate_documents(
    contract_ids: List[str],
    format_type: str = Query("pdf", description="Document format (pdf, docx, html, txt, markdown)"),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate documents for multiple contracts in batch.
    """
    supabase = get_supabase_client()

    # Get contracts data
    contracts_data = []
    for contract_id in contract_ids:
        response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
        contract_data = handle_supabase_response(response, f"Failed to retrieve contract {contract_id}")

        if contract_data:
            contracts_data.append(contract_data[0])

    if not contracts_data:
        raise not_found_error("Valid contracts")

    try:
        # Initialize document generator
        doc_generator = DocumentGeneratorService()

        # Custom branding options
        branding_options = {
            "company_name": "LegalAI",
            "letterhead": True,
            "footer_text": "Generated by LegalAI Advanced Document Generator"
        }

        # Generate documents in batch
        results = await doc_generator.generate_batch_documents(
            contracts=contracts_data,
            format_type=format_type,
            branding_options=branding_options
        )

        # Count successes and failures
        successful = sum(1 for r in results if r["success"])
        failed = len(results) - successful

        return {
            "success": True,
            "message": f"Batch generation completed: {successful} successful, {failed} failed",
            "results": results,
            "summary": {
                "total": len(results),
                "successful": successful,
                "failed": failed
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch document generation failed: {str(e)}")

@router.get("/{contract_id}/download-document/{file_path:path}")
async def download_generated_document(
    contract_id: str = Path(..., description="The ID of the contract"),
    file_path: str = Path(..., description="The path to the generated document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Download a generated contract document.
    """
    try:
        # Get signed URL for the file
        signed_url = StorageService.get_file_url(file_path=file_path, expires_in=3600)

        return {
            "download_url": signed_url,
            "expires_in": 3600
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get download URL: {str(e)}")

@router.put("/{contract_id}", response_model=Contract)
async def update_contract(
    contract: ContractUpdate,
    contract_id: str = Path(..., description="The ID of the contract to update"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific contract.
    """
    supabase = get_supabase_client()

    # Check if contract exists
    check_response = supabase.table("contracts").select("id").eq("id", contract_id).execute()

    # Handle contract existence check
    check_data = handle_supabase_response(check_response, "Failed to check contract existence")

    if not check_data:
        raise not_found_error("Contract")

    # Prepare update data
    update_data = contract.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()

    # Update the contract
    response = supabase.table("contracts").update(update_data).eq("id", contract_id).execute()

    # Handle contract update response
    updated_data = handle_supabase_response(response, "Failed to update contract")

    if not updated_data:
        raise HTTPException(status_code=500, detail="Contract update failed - no data returned")

    return updated_data[0]

@router.delete("/{contract_id}")
async def delete_contract(
    contract_id: str = Path(..., description="The ID of the contract to delete"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific contract.
    """
    supabase = get_supabase_client()

    # Check if contract exists
    check_response = supabase.table("contracts").select("id").eq("id", contract_id).execute()

    # Handle contract existence check
    check_data = handle_supabase_response(check_response, "Failed to check contract existence")

    if not check_data:
        raise not_found_error("Contract")

    # Delete the contract
    response = supabase.table("contracts").delete().eq("id", contract_id).execute()

    # Handle contract deletion response
    handle_supabase_response(response, "Failed to delete contract")

    return {"message": "Contract deleted successfully"}


@router.post("/{contract_id}/analyze", response_model=AIAnalysisResponse)
async def analyze_contract_ai(
    contract_id: str = Path(..., description="The ID of the contract to analyze"),
    analysis_type: str = Query("contract_analysis", description="Type of analysis to perform"),
    current_user: dict = Depends(get_current_user)
):
    """
    Perform AI analysis on a contract using the hybrid AI service.
    Returns comprehensive analysis including risk assessment, compliance check, and suggestions.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Get contract data with workspace validation
    response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
    contract_data = handle_supabase_response(response, "Failed to retrieve contract")

    if not contract_data:
        raise not_found_error("Contract")

    contract = contract_data[0]
    workspace_id = contract.get("workspace_id")

    if workspace_id:
        # Validate workspace access
        await validate_workspace_access(current_user["id"], workspace_id, supabase)

    try:
        # Extract text content for analysis
        contract_text = ""

        # Combine various text fields for analysis
        if contract.get("description"):
            contract_text += f"Description: {contract['description']}\n\n"

        if contract.get("clauses"):
            contract_text += "Clauses:\n"
            for clause in contract["clauses"]:
                if isinstance(clause, dict):
                    contract_text += f"- {clause.get('title', '')}: {clause.get('content', '')}\n"
                else:
                    contract_text += f"- {clause}\n"
            contract_text += "\n"

        if contract.get("custom_fields"):
            contract_text += "Additional Terms:\n"
            for key, value in contract["custom_fields"].items():
                contract_text += f"{key}: {value}\n"

        # If no substantial text content, return error
        if len(contract_text.strip()) < 50:
            raise HTTPException(
                status_code=400,
                detail="Contract does not contain sufficient text content for AI analysis"
            )

        # Perform AI analysis
        analysis_result = await ai_service.analyze_contract(contract_text, analysis_type)

        # Prepare analysis data for database storage
        analysis_data = {
            "id": str(uuid.uuid4()),
            "contract_id": contract_id,
            "risk_score": analysis_result.risk_score,
            "compliance_score": analysis_result.compliance_score,
            "language_clarity": analysis_result.language_clarity,
            "key_risks": analysis_result.key_risks,
            "suggestions": analysis_result.suggestions,
            "extracted_clauses": analysis_result.extracted_clauses,
            "compliance_issues": analysis_result.compliance_issues,
            "obligations": analysis_result.obligations,
            "workspace_id": workspace_id,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }

        # Store analysis result in database
        store_response = supabase.table("ai_analysis_results").insert(analysis_data).execute()
        stored_data = handle_supabase_response(store_response, "Failed to store analysis result")

        # Return comprehensive analysis result
        return {
            "success": True,
            "analysis_id": analysis_data["id"],
            "contract_id": contract_id,
            "analysis_type": analysis_type,
            "contract_type": analysis_result.contract_type,
            "risk_score": analysis_result.risk_score,
            "compliance_score": analysis_result.compliance_score,
            "language_clarity": analysis_result.language_clarity,
            "key_risks": analysis_result.key_risks,
            "suggestions": analysis_result.suggestions,
            "extracted_clauses": analysis_result.extracted_clauses,
            "compliance_issues": analysis_result.compliance_issues,
            "obligations": analysis_result.obligations,
            "confidence": analysis_result.confidence,
            "provider": analysis_result.provider,
            "processing_time": analysis_result.processing_time,
            "created_at": analysis_result.created_at.isoformat()
        }

    except Exception as e:
        logger.error(f"AI analysis failed for contract {contract_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"AI analysis failed: {str(e)}"
        )


@router.get("/{contract_id}/analysis", response_model=AIAnalysisResultSchema)
async def get_contract_analysis(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve the latest AI analysis result for a contract.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Get the latest analysis result for this contract
    response = supabase.table("ai_analysis_results").select("*").eq("contract_id", contract_id).order("created_at", desc=True).limit(1).execute()

    analysis_data = handle_supabase_response(response, "Failed to retrieve analysis")

    if not analysis_data:
        raise not_found_error("AI analysis result")

    return analysis_data[0]


@router.get("/{contract_id}/analysis/history", response_model=List[AIAnalysisResultSchema])
async def get_contract_analysis_history(
    contract_id: str = Path(..., description="The ID of the contract"),
    limit: int = Query(10, ge=1, le=50, description="Number of analysis results to return"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve the analysis history for a contract.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Get analysis history for this contract
    response = supabase.table("ai_analysis_results").select("*").eq("contract_id", contract_id).order("created_at", desc=True).limit(limit).execute()

    analysis_data = handle_supabase_response(response, "Failed to retrieve analysis history")

    return analysis_data or []
