"""
Approval Workflow Service for Averum Contracts

This service provides comprehensive approval workflow management including:
- Sequential and parallel approval processes
- Conditional routing based on contract attributes
- Escalation and timeout handling
- Workflow template management
- AI-enhanced approval routing
"""

import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import logging

from supabase import Client
from app.db.database import get_supabase_client
from app.services.notification_service import NotificationService
from app.services.ai_service import ai_service

logger = logging.getLogger(__name__)


class WorkflowType(str, Enum):
    """Types of approval workflows."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    HYBRID = "hybrid"


class ApprovalStatus(str, Enum):
    """Status of individual approvals."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    ESCALATED = "escalated"
    EXPIRED = "expired"
    SKIPPED = "skipped"


class WorkflowStatus(str, Enum):
    """Status of entire workflow."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    ESCALATED = "escalated"


class ApprovalWorkflowService:
    """Service for managing approval workflows."""

    def __init__(self, supabase_client: Optional[Client] = None):
        self.supabase = supabase_client or get_supabase_client()
        self.notification_service = NotificationService(supabase_client)

    async def create_workflow(
        self,
        contract_id: str,
        workspace_id: str,
        workflow_type: WorkflowType,
        approvers: List[Dict[str, Any]],
        created_by: str,
        template_id: Optional[str] = None,
        due_date: Optional[datetime] = None,
        escalation_rules: Optional[Dict[str, Any]] = None,
        conditions: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new approval workflow."""
        try:
            workflow_id = str(uuid.uuid4())
            
            # Prepare workflow data
            workflow_data = {
                "id": workflow_id,
                "contract_id": contract_id,
                "workspace_id": workspace_id,
                "workflow_type": workflow_type.value,
                "status": WorkflowStatus.DRAFT.value,
                "created_by": created_by,
                "created_at": datetime.utcnow().isoformat(),
                "due_date": due_date.isoformat() if due_date else None,
                "escalation_rules": escalation_rules or {},
                "conditions": conditions or {},
                "template_id": template_id
            }

            # Insert workflow
            response = self.supabase.table("approval_workflows").insert(workflow_data).execute()
            
            if hasattr(response, 'error') and response.error:
                raise Exception(f"Failed to create workflow: {response.error}")

            workflow = response.data[0]

            # Create individual approvals
            approvals = []
            for i, approver in enumerate(approvers):
                approval_data = {
                    "id": str(uuid.uuid4()),
                    "workflow_id": workflow_id,
                    "user_id": approver["user_id"],
                    "order": approver.get("order", i + 1),
                    "status": ApprovalStatus.PENDING.value,
                    "required": approver.get("required", True),
                    "role": approver.get("role", "approver"),
                    "conditions": approver.get("conditions", {}),
                    "created_at": datetime.utcnow().isoformat()
                }
                approvals.append(approval_data)

            if approvals:
                response = self.supabase.table("approvals").insert(approvals).execute()
                
                if hasattr(response, 'error') and response.error:
                    raise Exception(f"Failed to create approvals: {response.error}")

            # Start workflow if not draft
            if workflow_type != WorkflowType.CONDITIONAL:
                await self.start_workflow(workflow_id)

            return workflow

        except Exception as e:
            logger.error(f"Error creating approval workflow: {str(e)}")
            raise

    async def start_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Start an approval workflow."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                raise Exception("Workflow not found")

            workflow = workflow_response.data[0]
            
            # Update workflow status to active
            update_data = {
                "status": WorkflowStatus.ACTIVE.value,
                "started_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("approval_workflows").update(update_data).eq("id", workflow_id).execute()

            # Determine which approvers to notify based on workflow type
            if workflow["workflow_type"] == WorkflowType.SEQUENTIAL.value:
                await self._start_sequential_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.PARALLEL.value:
                await self._start_parallel_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.CONDITIONAL.value:
                await self._start_conditional_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.HYBRID.value:
                await self._start_hybrid_workflow(workflow_id)

            return workflow

        except Exception as e:
            logger.error(f"Error starting workflow: {str(e)}")
            raise

    async def _start_sequential_workflow(self, workflow_id: str):
        """Start sequential workflow - notify first approver only."""
        approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).order("order").execute()
        
        if approvals_response.data:
            first_approval = approvals_response.data[0]
            await self._notify_approver(first_approval)

    async def _start_parallel_workflow(self, workflow_id: str):
        """Start parallel workflow - notify all approvers."""
        approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).execute()
        
        for approval in approvals_response.data:
            await self._notify_approver(approval)

    async def _start_conditional_workflow(self, workflow_id: str):
        """Start conditional workflow - use AI to determine routing."""
        # Get workflow and contract details
        workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", workflow_id).execute()
        
        if not workflow_response.data:
            return

        workflow = workflow_response.data[0]
        contract = workflow.get("contracts")

        if contract:
            # Use AI to determine approval routing
            routing_decision = await self._ai_determine_routing(contract, workflow_id)
            
            # Apply routing decision
            await self._apply_routing_decision(workflow_id, routing_decision)

    async def _start_hybrid_workflow(self, workflow_id: str):
        """Start hybrid workflow - combination of sequential and parallel."""
        # Implementation for hybrid workflows
        # This could involve grouping approvers and processing groups sequentially
        # while processing approvers within each group in parallel
        pass

    async def _ai_determine_routing(self, contract: Dict[str, Any], workflow_id: str) -> Dict[str, Any]:
        """Use AI to determine optimal approval routing."""
        try:
            # Analyze contract for risk and complexity
            analysis_prompt = f"""
            Analyze this contract and determine the optimal approval routing:
            
            Contract Title: {contract.get('title', 'N/A')}
            Contract Type: {contract.get('type', 'N/A')}
            Contract Value: {contract.get('value', 'N/A')}
            Description: {contract.get('description', 'N/A')}
            
            Based on the contract details, recommend:
            1. Required approval levels (legal, finance, operations, executive)
            2. Approval order (sequential vs parallel)
            3. Priority level (low, medium, high, urgent)
            4. Estimated approval time
            
            Return a JSON response with routing recommendations.
            """

            # Get AI analysis
            ai_response = await ai_service.analyze_contract(analysis_prompt, "approval_routing")
            
            # Parse AI response and create routing decision
            routing_decision = {
                "required_approvers": ["legal", "finance"],  # Default fallback
                "workflow_type": "sequential",
                "priority": "medium",
                "estimated_time_hours": 48,
                "ai_confidence": ai_response.confidence if hasattr(ai_response, 'confidence') else 0.7
            }

            return routing_decision

        except Exception as e:
            logger.error(f"AI routing determination failed: {str(e)}")
            # Return default routing
            return {
                "required_approvers": ["legal"],
                "workflow_type": "sequential",
                "priority": "medium",
                "estimated_time_hours": 24,
                "ai_confidence": 0.0
            }

    async def _apply_routing_decision(self, workflow_id: str, routing_decision: Dict[str, Any]):
        """Apply AI routing decision to workflow."""
        # Update workflow based on AI decision
        # This would involve updating approver assignments, order, etc.
        pass

    async def _notify_approver(self, approval: Dict[str, Any]):
        """Send notification to an approver."""
        try:
            # Get workflow and contract details
            workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", approval["workflow_id"]).execute()
            
            if not workflow_response.data:
                return

            workflow = workflow_response.data[0]
            contract = workflow.get("contracts")

            # Create notification
            notification_data = {
                "user_id": approval["user_id"],
                "workspace_id": workflow["workspace_id"],
                "title": "Contract Approval Required",
                "message": f"Please review and approve contract: {contract.get('title', 'Unknown') if contract else 'Unknown'}",
                "type": "approval_request",
                "entity_id": workflow["contract_id"],
                "entity_type": "contract",
                "action_url": f"/app/contracts/{workflow['contract_id']}/approve",
                "metadata": {
                    "workflow_id": workflow["id"],
                    "approval_id": approval["id"],
                    "due_date": workflow.get("due_date"),
                    "priority": "medium"
                }
            }

            await self.notification_service.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error notifying approver: {str(e)}")

    async def approve_contract(
        self,
        approval_id: str,
        user_id: str,
        comments: Optional[str] = None
    ) -> Dict[str, Any]:
        """Approve a contract in the workflow."""
        try:
            # Update approval status
            approval_data = {
                "status": ApprovalStatus.APPROVED.value,
                "approved_at": datetime.utcnow().isoformat(),
                "comments": comments,
                "approved_by": user_id
            }

            response = self.supabase.table("approvals").update(approval_data).eq("id", approval_id).eq("user_id", user_id).execute()
            
            if not response.data:
                raise Exception("Approval not found or unauthorized")

            approval = response.data[0]

            # Check if workflow is complete
            await self._check_workflow_completion(approval["workflow_id"])

            return approval

        except Exception as e:
            logger.error(f"Error approving contract: {str(e)}")
            raise

    async def reject_contract(
        self,
        approval_id: str,
        user_id: str,
        reason: str
    ) -> Dict[str, Any]:
        """Reject a contract in the workflow."""
        try:
            # Update approval status
            approval_data = {
                "status": ApprovalStatus.REJECTED.value,
                "rejected_at": datetime.utcnow().isoformat(),
                "comments": reason,
                "rejected_by": user_id
            }

            response = self.supabase.table("approvals").update(approval_data).eq("id", approval_id).eq("user_id", user_id).execute()
            
            if not response.data:
                raise Exception("Approval not found or unauthorized")

            approval = response.data[0]

            # Mark entire workflow as rejected
            workflow_data = {
                "status": WorkflowStatus.REJECTED.value,
                "completed_at": datetime.utcnow().isoformat(),
                "rejection_reason": reason
            }

            self.supabase.table("approval_workflows").update(workflow_data).eq("id", approval["workflow_id"]).execute()

            return approval

        except Exception as e:
            logger.error(f"Error rejecting contract: {str(e)}")
            raise

    async def _check_workflow_completion(self, workflow_id: str):
        """Check if workflow is complete and update status accordingly."""
        try:
            # Get all approvals for this workflow
            approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).execute()
            
            if not approvals_response.data:
                return

            approvals = approvals_response.data
            required_approvals = [a for a in approvals if a.get("required", True)]
            
            # Check completion based on workflow type
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()
            workflow = workflow_response.data[0] if workflow_response.data else None
            
            if not workflow:
                return

            is_complete = False
            
            if workflow["workflow_type"] == WorkflowType.SEQUENTIAL.value:
                # All required approvals must be approved in order
                is_complete = all(a["status"] == ApprovalStatus.APPROVED.value for a in required_approvals)
            elif workflow["workflow_type"] == WorkflowType.PARALLEL.value:
                # All required approvals must be approved
                is_complete = all(a["status"] == ApprovalStatus.APPROVED.value for a in required_approvals)

            if is_complete:
                # Mark workflow as complete
                workflow_data = {
                    "status": WorkflowStatus.COMPLETED.value,
                    "completed_at": datetime.utcnow().isoformat()
                }

                self.supabase.table("approval_workflows").update(workflow_data).eq("id", workflow_id).execute()

                # Update contract status
                contract_data = {
                    "status": "approved"
                }

                self.supabase.table("contracts").update(contract_data).eq("id", workflow["contract_id"]).execute()

                # Notify contract creator
                await self._notify_workflow_completion(workflow_id)

        except Exception as e:
            logger.error(f"Error checking workflow completion: {str(e)}")

    async def _notify_workflow_completion(self, workflow_id: str):
        """Notify relevant parties of workflow completion."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                return

            workflow = workflow_response.data[0]
            contract = workflow.get("contracts")

            # Notify contract creator
            notification_data = {
                "user_id": workflow["created_by"],
                "workspace_id": workflow["workspace_id"],
                "title": "Contract Approved",
                "message": f"Contract '{contract.get('title', 'Unknown') if contract else 'Unknown'}' has been approved and is now active.",
                "type": "approval_completed",
                "entity_id": workflow["contract_id"],
                "entity_type": "contract",
                "action_url": f"/app/contracts/{workflow['contract_id']}"
            }

            await self.notification_service.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error notifying workflow completion: {str(e)}")

    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get the current status of a workflow."""
        try:
            # Get workflow with approvals
            workflow_response = self.supabase.table("approval_workflows").select("*, approvals(*), contracts(*)").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                raise Exception("Workflow not found")

            workflow = workflow_response.data[0]
            
            # Calculate progress
            approvals = workflow.get("approvals", [])
            total_approvals = len([a for a in approvals if a.get("required", True)])
            completed_approvals = len([a for a in approvals if a["status"] == ApprovalStatus.APPROVED.value])
            
            progress = (completed_approvals / total_approvals * 100) if total_approvals > 0 else 0

            return {
                "workflow": workflow,
                "progress": progress,
                "total_approvals": total_approvals,
                "completed_approvals": completed_approvals,
                "pending_approvals": total_approvals - completed_approvals
            }

        except Exception as e:
            logger.error(f"Error getting workflow status: {str(e)}")
            raise
