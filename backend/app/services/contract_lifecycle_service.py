import uuid
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from supabase import Client
from app.schemas.contract_lifecycle import (
    ContractVersion, ContractVersionCreate,
    ContractRenewal, ContractRenewalCreate, ContractRenewalUpdate,
    ContractLifecycleEvent, ContractLifecycleEventCreate,
    ContractReminder, ContractReminderCreate, ContractReminderUpdate,
    VersionComparison, VersionDiff,
    ContractLifecycleStats, ExpirationAlert,
    BulkRenewalRequest, BulkRenewalResponse,
    StatusTransitionRequest
)
import logging

logger = logging.getLogger(__name__)

class ContractLifecycleService:
    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client

    # Version Management Methods
    async def get_contract_versions(self, contract_id: str) -> List[ContractVersion]:
        """Get all versions of a contract."""
        try:
            response = self.supabase.table("contract_versions").select("*").eq("contract_id", contract_id).order("version_number", desc=True).execute()
            
            if response.data:
                return [ContractVersion(**version) for version in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching contract versions: {str(e)}")
            raise

    async def get_current_version(self, contract_id: str) -> Optional[ContractVersion]:
        """Get the current version of a contract."""
        try:
            response = self.supabase.table("contract_versions").select("*").eq("contract_id", contract_id).eq("is_current", True).single().execute()
            
            if response.data:
                return ContractVersion(**response.data)
            return None
        except Exception as e:
            logger.error(f"Error fetching current version: {str(e)}")
            return None

    async def create_version(self, version_data: ContractVersionCreate, user_id: str) -> ContractVersion:
        """Create a new version of a contract."""
        try:
            # Mark all previous versions as not current
            self.supabase.table("contract_versions").update({"is_current": False}).eq("contract_id", version_data.contract_id).execute()
            
            # Create new version
            version_dict = version_data.model_dump()
            version_dict["id"] = str(uuid.uuid4())
            version_dict["created_by"] = {"id": user_id, "name": "User"}  # This should be populated with actual user data
            version_dict["created_at"] = datetime.utcnow().isoformat()
            version_dict["is_current"] = True
            
            response = self.supabase.table("contract_versions").insert(version_dict).execute()
            
            if response.data:
                return ContractVersion(**response.data[0])
            raise Exception("Failed to create version")
        except Exception as e:
            logger.error(f"Error creating contract version: {str(e)}")
            raise

    async def compare_versions(self, version1_id: str, version2_id: str) -> VersionComparison:
        """Compare two versions of a contract."""
        try:
            # Get both versions
            response1 = self.supabase.table("contract_versions").select("*").eq("id", version1_id).single().execute()
            response2 = self.supabase.table("contract_versions").select("*").eq("id", version2_id).single().execute()
            
            if not response1.data or not response2.data:
                raise Exception("One or both versions not found")
            
            version1 = ContractVersion(**response1.data)
            version2 = ContractVersion(**response2.data)
            
            # Generate differences
            differences = self._generate_version_diff(version1.content, version2.content)
            
            return VersionComparison(
                from_version=version1,
                to_version=version2,
                differences=differences,
                summary=f"Comparison between version {version1.version_number} and {version2.version_number}"
            )
        except Exception as e:
            logger.error(f"Error comparing versions: {str(e)}")
            raise

    def _generate_version_diff(self, content1: Dict[str, Any], content2: Dict[str, Any]) -> List[VersionDiff]:
        """Generate differences between two version contents."""
        differences = []
        
        # Compare all fields
        all_keys = set(content1.keys()) | set(content2.keys())
        
        for key in all_keys:
            if key not in content1:
                differences.append(VersionDiff(
                    field=key,
                    old_value=None,
                    new_value=content2[key],
                    change_type="added"
                ))
            elif key not in content2:
                differences.append(VersionDiff(
                    field=key,
                    old_value=content1[key],
                    new_value=None,
                    change_type="removed"
                ))
            elif content1[key] != content2[key]:
                differences.append(VersionDiff(
                    field=key,
                    old_value=content1[key],
                    new_value=content2[key],
                    change_type="modified"
                ))
        
        return differences

    # Renewal Management Methods
    async def get_contract_renewals(self, contract_id: str) -> List[ContractRenewal]:
        """Get all renewals for a contract."""
        try:
            response = self.supabase.table("contract_renewals").select("*").eq("contract_id", contract_id).order("requested_at", desc=True).execute()
            
            if response.data:
                return [ContractRenewal(**renewal) for renewal in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching contract renewals: {str(e)}")
            raise

    async def create_renewal(self, renewal_data: ContractRenewalCreate, user_id: str) -> ContractRenewal:
        """Create a new contract renewal request."""
        try:
            renewal_dict = renewal_data.model_dump()
            renewal_dict["id"] = str(uuid.uuid4())
            renewal_dict["requested_by"] = {"id": user_id, "name": "User"}
            renewal_dict["requested_at"] = datetime.utcnow().isoformat()
            
            response = self.supabase.table("contract_renewals").insert(renewal_dict).execute()
            
            if response.data:
                return ContractRenewal(**response.data[0])
            raise Exception("Failed to create renewal")
        except Exception as e:
            logger.error(f"Error creating contract renewal: {str(e)}")
            raise

    async def approve_renewal(self, renewal_id: str, user_id: str) -> ContractRenewal:
        """Approve a contract renewal."""
        try:
            update_data = {
                "renewal_status": "approved",
                "approved_by": {"id": user_id, "name": "User"},
                "approved_at": datetime.utcnow().isoformat()
            }
            
            response = self.supabase.table("contract_renewals").update(update_data).eq("id", renewal_id).execute()
            
            if response.data:
                return ContractRenewal(**response.data[0])
            raise Exception("Failed to approve renewal")
        except Exception as e:
            logger.error(f"Error approving renewal: {str(e)}")
            raise

    async def complete_renewal(self, renewal_id: str) -> ContractRenewal:
        """Mark a renewal as completed and update the contract."""
        try:
            # Get renewal details
            renewal_response = self.supabase.table("contract_renewals").select("*").eq("id", renewal_id).single().execute()
            
            if not renewal_response.data:
                raise Exception("Renewal not found")
            
            renewal = ContractRenewal(**renewal_response.data)
            
            # Update the contract's expiry date
            contract_update = {
                "expiry_date": renewal.new_expiry_date.isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("contracts").update(contract_update).eq("id", renewal.contract_id).execute()
            
            # Mark renewal as completed
            renewal_update = {
                "renewal_status": "completed",
                "completed_at": datetime.utcnow().isoformat()
            }
            
            response = self.supabase.table("contract_renewals").update(renewal_update).eq("id", renewal_id).execute()
            
            if response.data:
                return ContractRenewal(**response.data[0])
            raise Exception("Failed to complete renewal")
        except Exception as e:
            logger.error(f"Error completing renewal: {str(e)}")
            raise

    # Lifecycle Event Methods
    async def get_lifecycle_events(self, contract_id: str) -> List[ContractLifecycleEvent]:
        """Get all lifecycle events for a contract."""
        try:
            response = self.supabase.table("contract_lifecycle_events").select("*").eq("contract_id", contract_id).order("triggered_at", desc=True).execute()
            
            if response.data:
                return [ContractLifecycleEvent(**event) for event in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching lifecycle events: {str(e)}")
            raise

    async def create_lifecycle_event(self, event_data: ContractLifecycleEventCreate) -> ContractLifecycleEvent:
        """Create a new lifecycle event."""
        try:
            event_dict = event_data.model_dump()
            event_dict["id"] = str(uuid.uuid4())
            event_dict["triggered_at"] = datetime.utcnow().isoformat()
            
            response = self.supabase.table("contract_lifecycle_events").insert(event_dict).execute()
            
            if response.data:
                return ContractLifecycleEvent(**response.data[0])
            raise Exception("Failed to create lifecycle event")
        except Exception as e:
            logger.error(f"Error creating lifecycle event: {str(e)}")
            raise

    # Reminder Methods
    async def get_contract_reminders(self, contract_id: str) -> List[ContractReminder]:
        """Get all reminders for a contract."""
        try:
            response = self.supabase.table("contract_reminders").select("*").eq("contract_id", contract_id).order("reminder_date").execute()
            
            if response.data:
                return [ContractReminder(**reminder) for reminder in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching contract reminders: {str(e)}")
            raise

    async def get_pending_reminders(self, workspace_id: str) -> List[ContractReminder]:
        """Get all pending reminders for a workspace."""
        try:
            current_time = datetime.utcnow().isoformat()
            response = self.supabase.table("contract_reminders").select("*").eq("workspace_id", workspace_id).eq("sent", False).lte("reminder_date", current_time).execute()
            
            if response.data:
                return [ContractReminder(**reminder) for reminder in response.data]
            return []
        except Exception as e:
            logger.error(f"Error fetching pending reminders: {str(e)}")
            raise

    async def mark_reminder_sent(self, reminder_id: str) -> ContractReminder:
        """Mark a reminder as sent."""
        try:
            update_data = {
                "sent": True,
                "sent_at": datetime.utcnow().isoformat()
            }

            response = self.supabase.table("contract_reminders").update(update_data).eq("id", reminder_id).execute()

            if response.data:
                return ContractReminder(**response.data[0])
            raise Exception("Failed to mark reminder as sent")
        except Exception as e:
            logger.error(f"Error marking reminder as sent: {str(e)}")
            raise

    # Analytics and Reporting Methods
    async def get_lifecycle_stats(self, workspace_id: str) -> ContractLifecycleStats:
        """Get lifecycle statistics for a workspace."""
        try:
            # Get total contracts
            total_response = self.supabase.table("contracts").select("id", count="exact").eq("workspace_id", workspace_id).execute()
            total_contracts = total_response.count or 0

            # Get active contracts
            active_response = self.supabase.table("contracts").select("id", count="exact").eq("workspace_id", workspace_id).eq("status", "active").execute()
            active_contracts = active_response.count or 0

            # Get expired contracts
            expired_response = self.supabase.table("contracts").select("id", count="exact").eq("workspace_id", workspace_id).eq("status", "expired").execute()
            expired_contracts = expired_response.count or 0

            # Get pending renewals
            pending_renewals_response = self.supabase.table("contract_renewals").select("id", count="exact").eq("workspace_id", workspace_id).eq("renewal_status", "pending").execute()
            pending_renewals = pending_renewals_response.count or 0

            # Get upcoming expirations (next 30 days)
            thirty_days_from_now = (datetime.utcnow() + timedelta(days=30)).isoformat()
            upcoming_response = self.supabase.table("contracts").select("id", count="exact").eq("workspace_id", workspace_id).eq("status", "active").lte("expiry_date", thirty_days_from_now).execute()
            upcoming_expirations = upcoming_response.count or 0

            return ContractLifecycleStats(
                total_contracts=total_contracts,
                active_contracts=active_contracts,
                expired_contracts=expired_contracts,
                pending_renewals=pending_renewals,
                upcoming_expirations=upcoming_expirations
            )
        except Exception as e:
            logger.error(f"Error fetching lifecycle stats: {str(e)}")
            raise

    async def get_expiration_alerts(self, workspace_id: str, days_ahead: int = 30) -> List[ExpirationAlert]:
        """Get contracts expiring within the specified number of days."""
        try:
            target_date = (datetime.utcnow() + timedelta(days=days_ahead)).isoformat()
            current_date = datetime.utcnow().isoformat()

            response = self.supabase.table("contracts").select("id, title, expiry_date, status, workspace_id").eq("workspace_id", workspace_id).eq("status", "active").gte("expiry_date", current_date).lte("expiry_date", target_date).execute()

            alerts = []
            if response.data:
                for contract in response.data:
                    expiry_date = datetime.fromisoformat(contract["expiry_date"].replace('Z', '+00:00'))
                    days_until_expiry = (expiry_date - datetime.utcnow().replace(tzinfo=expiry_date.tzinfo)).days

                    alerts.append(ExpirationAlert(
                        contract_id=contract["id"],
                        contract_title=contract["title"],
                        expiry_date=expiry_date,
                        days_until_expiry=days_until_expiry,
                        status=contract["status"],
                        workspace_id=contract["workspace_id"]
                    ))

            return alerts
        except Exception as e:
            logger.error(f"Error fetching expiration alerts: {str(e)}")
            raise

    # Bulk Operations
    async def bulk_renewal(self, bulk_request: BulkRenewalRequest, user_id: str, workspace_id: str) -> BulkRenewalResponse:
        """Process bulk renewal requests."""
        successful_renewals = []
        failed_renewals = []

        for contract_id in bulk_request.contract_ids:
            try:
                # Get contract details
                contract_response = self.supabase.table("contracts").select("*").eq("id", contract_id).single().execute()

                if not contract_response.data:
                    failed_renewals.append({"contract_id": contract_id, "error": "Contract not found"})
                    continue

                contract = contract_response.data

                # Create renewal request
                renewal_data = ContractRenewalCreate(
                    contract_id=contract_id,
                    original_expiry_date=datetime.fromisoformat(contract["expiry_date"].replace('Z', '+00:00')),
                    new_expiry_date=bulk_request.new_expiry_date,
                    renewal_period=bulk_request.renewal_period,
                    renewal_terms=bulk_request.renewal_terms,
                    workspace_id=workspace_id
                )

                renewal = await self.create_renewal(renewal_data, user_id)
                successful_renewals.append(renewal.id)

            except Exception as e:
                failed_renewals.append({"contract_id": contract_id, "error": str(e)})

        return BulkRenewalResponse(
            successful_renewals=successful_renewals,
            failed_renewals=failed_renewals,
            total_processed=len(bulk_request.contract_ids)
        )

    # Status Management
    async def transition_contract_status(self, request: StatusTransitionRequest, user_id: str) -> Dict[str, Any]:
        """Transition a contract to a new status."""
        try:
            # Get current contract
            contract_response = self.supabase.table("contracts").select("*").eq("id", request.contract_id).single().execute()

            if not contract_response.data:
                raise Exception("Contract not found")

            contract = contract_response.data
            old_status = contract["status"]

            # Update contract status
            update_data = {
                "status": request.new_status,
                "updated_at": datetime.utcnow().isoformat()
            }

            if request.effective_date:
                update_data["effective_date"] = request.effective_date.isoformat()

            response = self.supabase.table("contracts").update(update_data).eq("id", request.contract_id).execute()

            if not response.data:
                raise Exception("Failed to update contract status")

            # Create lifecycle event
            event_data = ContractLifecycleEventCreate(
                contract_id=request.contract_id,
                event_type="status_changed",
                event_description=f"Status changed from {old_status} to {request.new_status}",
                event_data={
                    "old_status": old_status,
                    "new_status": request.new_status,
                    "reason": request.reason
                },
                workspace_id=contract["workspace_id"],
                triggered_by=user_id
            )

            await self.create_lifecycle_event(event_data)

            return {
                "contract_id": request.contract_id,
                "old_status": old_status,
                "new_status": request.new_status,
                "updated_at": update_data["updated_at"]
            }
        except Exception as e:
            logger.error(f"Error transitioning contract status: {str(e)}")
            raise

    # Automated Status Updates
    async def update_expired_contracts(self, workspace_id: str) -> Dict[str, int]:
        """Update contracts that have passed their expiry date to expired status."""
        try:
            current_time = datetime.utcnow().isoformat()

            # Find active contracts that have expired
            response = self.supabase.table("contracts").select("id, title").eq("workspace_id", workspace_id).eq("status", "active").lt("expiry_date", current_time).execute()

            expired_count = 0
            if response.data:
                contract_ids = [contract["id"] for contract in response.data]

                # Update all expired contracts
                update_response = self.supabase.table("contracts").update({
                    "status": "expired",
                    "updated_at": current_time
                }).in_("id", contract_ids).execute()

                expired_count = len(update_response.data) if update_response.data else 0

                # Create lifecycle events for each expired contract
                for contract in response.data:
                    event_data = ContractLifecycleEventCreate(
                        contract_id=contract["id"],
                        event_type="expired",
                        event_description=f"Contract '{contract['title']}' automatically expired",
                        event_data={"automated": True},
                        workspace_id=workspace_id,
                        triggered_by="system"
                    )
                    await self.create_lifecycle_event(event_data)

            return {"expired_contracts": expired_count}
        except Exception as e:
            logger.error(f"Error updating expired contracts: {str(e)}")
            raise
