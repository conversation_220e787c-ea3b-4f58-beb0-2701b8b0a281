"""
Authentication and authorization utilities for the Averum Contracts backend.
<PERSON><PERSON> Clerk JWT token validation and workspace access control.
"""

from jose import jwt, JWTError
import httpx
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
from app.core.config import settings
from app.db.database import get_supabase_client, get_authenticated_supabase_client
import logging

logger = logging.getLogger(__name__)

# Security scheme for JWT tokens
security = HTTPBearer()


class ClerkJWTValidator:
    """Validates Clerk JWT tokens using <PERSON>'s JWKS endpoint."""
    
    def __init__(self):
        self.jwks_cache = {}
        self.jwks_url = None
        
    async def get_jwks(self) -> Dict[str, Any]:
        """Fetch JWKS from Clerk's endpoint."""
        if not self.jwks_url:
            # Extract the domain from the publishable key
            if settings.CLERK_PUBLISHABLE_KEY.startswith("pk_test_"):
                # For test keys, decode the base64 domain
                import base64
                try:
                    encoded_domain = settings.CLERK_PUBLISHABLE_KEY.split("_")[2]
                    domain = base64.b64decode(encoded_domain + "==").decode('utf-8').rstrip('$')
                    self.jwks_url = f"https://{domain}/.well-known/jwks.json"
                except Exception as e:
                    logger.error(f"Failed to decode Clerk domain: {e}")
                    # Fallback to hardcoded domain for this specific key
                    self.jwks_url = "https://definite-opossum-1.clerk.accounts.dev/.well-known/jwks.json"
            else:
                # For production keys
                self.jwks_url = "https://clerk.your-domain.com/.well-known/jwks.json"
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.jwks_url)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch JWKS: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to validate token"
            )
    
    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate a Clerk JWT token and return the payload."""
        # SECURITY: Only allow development bypass in very specific conditions
        if (settings.ENVIRONMENT == "development" and
            settings.CLERK_SECRET_KEY == "test_clerk_secret_key" and
            token == "dev_bypass_token"):
            logger.warning("Using development mode authentication bypass - THIS SHOULD NOT BE USED IN PRODUCTION")
            # Return a mock user for development (using existing user ID that has access to demo workspaces)
            return {
                "id": "user_2qKJKJKJKJKJKJKJKJKJKJKJKJKJ",
                "email": "<EMAIL>",
                "first_name": "Kelly",
                "last_name": "Womade",
                "sub": "user_2qKJKJKJKJKJKJKJKJKJKJKJKJKJ"
            }

        try:
            # Decode the token header to get the key ID
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get("kid")
            
            if not kid:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing key ID"
                )
            
            # Get JWKS and find the matching key
            jwks = await self.get_jwks()
            key = None
            
            for jwk in jwks.get("keys", []):
                if jwk.get("kid") == kid:
                    # For python-jose, we use the JWK directly
                    key = jwk
                    break
            
            if not key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: key not found"
                )
            
            # Verify and decode the token
            payload = jwt.decode(
                token,
                key,
                algorithms=["RS256"],
                options={"verify_aud": False}  # Clerk doesn't use standard aud claim
            )
            
            return payload
            
        except JWTError as e:
            if "expired" in str(e).lower():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired"
                )
            else:
                logger.error(f"Invalid token: {e}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error"
            )


# Global JWT validator instance
jwt_validator = ClerkJWTValidator()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Extract and validate the current user from the JWT token.
    Returns user information from the token payload.
    """
    try:
        token = credentials.credentials
        payload = await jwt_validator.validate_token(token)
        
        # Extract user information from the payload
        user_info = {
            "id": payload.get("sub") or payload.get("id"),
            "email": payload.get("email", ""),
            "first_name": payload.get("first_name", ""),
            "last_name": payload.get("last_name", ""),
            "full_name": payload.get("name", ""),
        }
        
        # Ensure we have a user ID
        if not user_info["id"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: missing user ID"
            )
        
        return user_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting user from token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )


async def validate_workspace_access(
    user_id: str,
    workspace_id: str,
    supabase_client=None
) -> bool:
    """
    Validate that a user has access to a specific workspace.
    Raises HTTPException if access is denied.

    Note: This function now uses the authenticated client with RLS,
    so the database will automatically filter results based on user access.
    """
    if not supabase_client:
        # Use authenticated client that sets user context for RLS
        supabase_client = get_authenticated_supabase_client(user_id)

    try:
        # Check if user is a member of the workspace
        # With RLS enabled, this will automatically filter to only workspaces the user has access to
        response = supabase_client.table("workspace_members").select("*").eq(
            "user_id", user_id
        ).eq("workspace_id", workspace_id).execute()

        # Handle Supabase response
        has_error = hasattr(response, 'error') and response.error
        has_data = hasattr(response, 'data') and response.data

        if has_error:
            logger.error(f"Database error checking workspace access: {response.error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to validate workspace access"
            )

        if not has_data or not response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have access to this workspace"
            )

        return True

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating workspace access: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate workspace access"
        )


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """
    Optional authentication dependency that returns None if no token is provided.
    Useful for endpoints that work with or without authentication.
    """
    if not credentials:
        return None

    try:
        return get_current_user(credentials)
    except HTTPException:
        return None


def get_authenticated_db_client(current_user: Dict[str, Any]):
    """
    Get a Supabase client with the current user's context set for RLS.
    Use this in endpoints that need database access with proper security.

    Usage:
        supabase = get_authenticated_db_client(current_user)
        response = supabase.table("contracts").select("*").execute()
    """
    return get_authenticated_supabase_client(current_user["id"])
