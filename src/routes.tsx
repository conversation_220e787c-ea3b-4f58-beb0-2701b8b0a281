import { RouteObject, useParams } from "react-router-dom";
import MainLayout from "./components/layout/MainLayout";
import Home from "./components/home";
import ContractManagement from "./components/contracts/ContractManagement";
import RepositoryPage from "./components/repository/RepositoryPage";
import ApprovalsPage from "./components/approvals/ApprovalsPage";
import SettingsPage from "./components/settings/SettingsPage";
import WorkspacePage from "./components/workspace/WorkspacePage";
import ContractWizard from "./components/contracts/contract-wizard/ContractWizard";
import ContractTemplatesPage from "./components/contracts/ContractTemplatesPage";
import AnalyticsPage from "./components/analytics/AnalyticsPage";
import ContractAIAnalysisDashboard from "./components/contracts/ContractAIAnalysisDashboard";
import AIInsightsPage from "./components/contracts/AIInsightsPage";
import ModernClauseLibrary from "./components/contracts/ModernClauseLibrary";
import ElectronicSignatureFlow from "./components/signature/ElectronicSignatureFlow";
import DashboardPage from "./components/dashboard/DashboardPage";
import ContractCreationMethod from "./components/contracts/ContractCreationMethod";
import LandingPage from "./components/landing/LandingPage";
import { ThemeProvider } from "./lib/theme-provider";



// Auth components
import SignIn from "./components/auth/SignIn";
import SignUp from "./components/auth/SignUp";
import UserProfile from "./components/auth/UserProfile";
import ProtectedRoute from "./components/auth/ProtectedRoute";

// Import components directly
import { DocumentPreviewEngine } from "@/engines/document-engine";
import ContractVersionHistory from "./components/contracts/ContractVersionHistory";
import { WorkspaceDebugTest } from "./components/debug/WorkspaceDebugTest";
import ApprovalWorkflow from "./components/approvals/ApprovalWorkflow";
import ClauseCategorizationSystem from "./components/contracts/ClauseCategorizationSystem";
import NotificationsPage from "./components/notifications/NotificationsPage";
import SmartClauseSuggestions from "./components/contracts/SmartClauseSuggestions";
import UserRolesPermissions from "./components/settings/UserRolesPermissions";
import ActivityHistory from "./components/activity/ActivityHistory";

// Wrapper component for protected routes with theme provider
const ProtectedAppWrapper = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="averum-theme">
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    </ThemeProvider>
  );
};

// Wrapper components for routes with params
const ContractPreviewWrapper = () => {
  const { contractId } = useParams();

  // Sample contract content for preview
  const sampleContent = `
    <h1>Contract Preview - ${contractId || "sample-contract-123"}</h1>
    <p>This is a sample contract document for preview purposes.</p>
    <h2>Terms and Conditions</h2>
    <p>Sample terms and conditions content...</p>
  `;

  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <div className="h-[800px]">
        <DocumentPreviewEngine
          content={sampleContent}
          documentTitle={`Contract Preview - ${contractId || "sample-contract-123"}`}
          showZoomControls={true}
          showPrintButton={true}
          showDownloadButton={true}
          showFullscreenButton={true}
          className="h-full"
        />
      </div>
    </div>
  );
};

const ContractVersionWrapper = () => {
  const { contractId } = useParams();
  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Contract Version History</h1>
          <p className="text-muted-foreground">
            Track all changes and versions for contract {contractId}
          </p>
        </div>
        <ContractVersionHistory contractId={contractId || "sample-contract-123"} />
      </div>
    </div>
  );
};

const ApprovalWorkflowWrapper = () => {
  const { contractId } = useParams();
  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <ApprovalWorkflow
        contractId={contractId || "sample-contract-123"}
        contractTitle={`Contract Approval - ${contractId || "sample-contract-123"}`}
        approvers={[
          {
            id: "approver-1",
            name: "John Smith",
            role: "Legal Director",
            status: "approved",
            order: 1,
            completedAt: "2023-07-10T14:30:00Z",
            comments: "Looks good to me. All legal requirements are met.",
          },
          {
            id: "approver-2",
            name: "Jane Doe",
            role: "Finance Manager",
            status: "current",
            order: 2,
          },
          {
            id: "approver-3",
            name: "Alice Johnson",
            role: "Operations Director",
            status: "pending",
            order: 3,
          },
        ]}
        approvalProcess="sequential"
        dueDate="2023-07-20T23:59:59Z"
        onApprove={(contractId, comments) => console.log("Approved contract:", contractId, "with comments:", comments)}
        onReject={(contractId, reason) => console.log("Rejected contract:", contractId, "with reason:", reason)}
        onAddApprover={(approver) => console.log("Added approver:", approver)}
        onViewContract={(contractId) => console.log("View contract:", contractId)}
      />
    </div>
  );
};



const routes: RouteObject[] = [
  // Landing page route (public) - default route
  {
    path: "/",
    element: <LandingPage />,
  },
  // Auth routes
  {
    path: "/sign-in/*",
    element: <SignIn />,
  },
  {
    path: "/sign-up/*",
    element: <SignUp />,
  },
  // Protected routes
  {
    path: "/app",
    element: <ProtectedAppWrapper />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: "dashboard",
        element: <DashboardPage />,
      },
      {
        path: "contracts",
        element: <ContractManagement />,
      },
      {
        path: "contracts/create",
        element: <ContractCreationMethod />,
      },
      {
        path: "contracts/new",
        element: <ContractCreationMethod />,
      },
      {
        path: "contracts/import",
        element: <ContractCreationMethod />,
      },
      {
        path: "contracts/templates",
        element: <ContractTemplatesPage />,
      },
      {
        path: "contracts/wizard",
        element: <ContractWizard />,
      },
      {
        path: "contracts/analysis/:contractId",
        element: <ContractAIAnalysisDashboard />,
      },
      {
        path: "contracts/ai-insights",
        element: <AIInsightsPage />,
      },
      {
        path: "repository",
        element: <RepositoryPage />,
      },
      {
        path: "approvals",
        element: <ApprovalsPage />,
      },
      {
        path: "settings",
        element: <SettingsPage />,
      },
      {
        path: "debug",
        element: <WorkspaceDebugTest />,
      },
      {
        path: "profile",
        element: <UserProfile />,
      },
      {
        path: "workspaces",
        element: <WorkspacePage />,
      },
      {
        path: "analytics",
        element: <AnalyticsPage />,
      },
      {
        path: "notifications",
        element: <NotificationsPage />,
      },
      {
        path: "activity",
        element: <ActivityHistory />,
      },
      {
        path: "clause-library",
        element: <ModernClauseLibrary />,
      },
      {
        path: "signature/:documentId",
        element: <ElectronicSignatureFlow />,
      },

      // New integrated routes
      {
        path: "contracts/preview/:contractId",
        element: <ContractPreviewWrapper />,
      },
      {
        path: "contracts/versions/:contractId",
        element: <ContractVersionWrapper />,
      },
      {
        path: "approvals/workflow/:contractId",
        element: <ApprovalWorkflowWrapper />,
      },
      {
        path: "clauses/categorization",
        element: (
          <div className="w-full h-full bg-background p-6 overflow-auto">
            <ClauseCategorizationSystem
              onCategoryCreate={(category) => console.log("Category created:", category)}
              onCategoryUpdate={(category) => console.log("Category updated:", category)}
              onCategoryDelete={(categoryId) => console.log("Category deleted:", categoryId)}
              onTagCreate={(tag) => console.log("Tag created:", tag)}
              onTagUpdate={(tag) => console.log("Tag updated:", tag)}
              onTagDelete={(tagId) => console.log("Tag deleted:", tagId)}
            />
          </div>
        ),
      },
      {
        path: "clauses/suggestions",
        element: (
          <div className="w-full h-full bg-background p-6 overflow-auto">
            <SmartClauseSuggestions
              contractContext={{
                contractType: "Service Agreement",
                jurisdiction: "California, USA",
                industry: "Technology",
                parties: [
                  { type: "Client", name: "Acme Corporation" },
                  { type: "Service Provider", name: "Tech Solutions Inc." },
                ],
                value: 120000,
                currency: "USD",
              }}
              onSelectClause={(clause) => console.log("Clause selected:", clause)}
            />
          </div>
        ),
      },
      {
        path: "settings/roles",
        element: (
          <div className="w-full h-full bg-background p-6 overflow-auto">
            <UserRolesPermissions
              onRoleCreate={(role) => console.log("Role created:", role)}
              onRoleUpdate={(role) => console.log("Role updated:", role)}
              onRoleDelete={(roleId) => console.log("Role deleted:", roleId)}
              onUserRoleUpdate={(userId, roleId) => console.log("User role updated:", userId, roleId)}
            />
          </div>
        ),
      },



    ],
  },
];

export default routes;
