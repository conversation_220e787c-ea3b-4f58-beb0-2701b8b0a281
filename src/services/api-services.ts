import { api } from '@/lib/api';
import type { ApiResponse } from '@/lib/api';

// Types
import type {
  Contract,
  ContractCreate,
  ContractUpdate,
  Template,
  TemplateCreate,
  TemplateUpdate,
  Workspace,
  WorkspaceCreate,
  WorkspaceUpdate,
  User,
  UserUpdate,
  Role,
  RoleCreate,
  RoleUpdate,
  Permission,
  WorkspaceMember,
  AIAnalysisResult,
  AIAnalysisRequest,
  DashboardSummary,
  ContractActivity,
  ContractTypeDistribution,
  RecentActivity,
  AnalyticsSummary,
  PerformanceMetric,
  Document,
  DocumentCreate,
  DocumentUpdate,
  FileInfo,
  Notification,
  NotificationCreate,
  NotificationUpdate,
  NotificationFilters,
  NotificationSummary,
  NotificationPreferences,
  NotificationBulkUpdate,
  NotificationBulkDelete
} from '@/services/api-types';

// Contract API Services
export const ContractService = {
  /**
   * Get all contracts with filtering (workspace_id is required for security)
   */
  async getContracts(params: {
    workspace_id: string;
    status?: string;
    type?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }, token?: string): Promise<ApiResponse<Contract[]>> {
    // Validate required workspace_id
    if (!params.workspace_id) {
      throw new Error('workspace_id is required for security');
    }

    // Build query string from params
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const endpoint = `/contracts?${queryString}`;

    return api.get<Contract[]>(endpoint, {}, token);
  },

  /**
   * Get a specific contract by ID
   */
  async getContract(id: string, token?: string): Promise<ApiResponse<Contract>> {
    return api.get<Contract>(`/contracts/${id}`, {}, token);
  },

  /**
   * Create a new contract
   */
  async createContract(data: ContractCreate, token?: string): Promise<ApiResponse<Contract>> {
    return api.post<Contract>('/contracts', data, {}, token);
  },

  /**
   * Update an existing contract
   */
  async updateContract(id: string, data: ContractUpdate, token?: string): Promise<ApiResponse<Contract>> {
    return api.put<Contract>(`/contracts/${id}`, data, {}, token);
  },

  /**
   * Delete a contract
   */
  async deleteContract(id: string, token?: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/contracts/${id}`, {}, token);
  },

  /**
   * Generate a document from a contract in various formats
   */
  async generateDocument(
    contractId: string,
    format: 'pdf' | 'docx' | 'html' | 'txt' | 'markdown' = 'pdf',
    templateName?: string,
    token?: string
  ): Promise<ApiResponse<{
    success: boolean;
    message: string;
    file_info: {
      id: string;
      filename: string;
      content_type: string;
      size: number;
      url: string;
      path: string;
      generated_at: string;
    };
  }>> {
    const params = new URLSearchParams();
    params.append('format_type', format);
    if (templateName) {
      params.append('template_name', templateName);
    }

    return api.post<{
      success: boolean;
      message: string;
      file_info: {
        id: string;
        filename: string;
        content_type: string;
        size: number;
        url: string;
        path: string;
        generated_at: string;
      };
    }>(`/contracts/${contractId}/generate-document?${params.toString()}`, {}, {}, token);
  },

  /**
   * Generate documents for multiple contracts in batch
   */
  async batchGenerateDocuments(
    contractIds: string[],
    format: 'pdf' | 'docx' | 'html' | 'txt' | 'markdown' = 'pdf',
    token?: string
  ): Promise<ApiResponse<{
    success: boolean;
    message: string;
    results: Array<{
      contract_id: string;
      success: boolean;
      file_info?: {
        id: string;
        filename: string;
        content_type: string;
        size: number;
        url: string;
        path: string;
        generated_at: string;
      };
      error?: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }>> {
    const params = new URLSearchParams();
    params.append('format_type', format);

    return api.post<{
      success: boolean;
      message: string;
      results: Array<{
        contract_id: string;
        success: boolean;
        file_info?: {
          id: string;
          filename: string;
          content_type: string;
          size: number;
          url: string;
          path: string;
          generated_at: string;
        };
        error?: string;
      }>;
      summary: {
        total: number;
        successful: number;
        failed: number;
      };
    }>(`/contracts/batch-generate-documents?${params.toString()}`, contractIds, {}, token);
  },

  /**
   * Get download URL for a generated contract document
   */
  async getDocumentDownloadUrl(
    contractId: string,
    filePath: string,
    token?: string
  ): Promise<ApiResponse<{
    download_url: string;
    expires_in: number;
  }>> {
    return api.get<{
      download_url: string;
      expires_in: number;
    }>(`/contracts/${contractId}/download-document/${encodeURIComponent(filePath)}`, {}, token);
  }
};

// Template API Services
export const TemplateService = {
  /**
   * Get all templates with filtering (workspace_id is required for security)
   */
  async getTemplates(params: {
    workspace_id: string;
    type?: string;
    industry?: string;
    complexity?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }): Promise<ApiResponse<Template[]>> {
    // Validate required workspace_id
    if (!params.workspace_id) {
      throw new Error('workspace_id is required for security');
    }

    // Build query string from params
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const endpoint = `/templates?${queryString}`;

    return api.get<Template[]>(endpoint);
  },

  /**
   * Get a specific template by ID
   */
  async getTemplate(id: string): Promise<ApiResponse<Template>> {
    return api.get<Template>(`/templates/${id}`);
  },

  /**
   * Create a new template
   */
  async createTemplate(data: TemplateCreate): Promise<ApiResponse<Template>> {
    return api.post<Template>('/templates', data);
  },

  /**
   * Update an existing template
   */
  async updateTemplate(id: string, data: TemplateUpdate): Promise<ApiResponse<Template>> {
    return api.put<Template>(`/templates/${id}`, data);
  },

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/templates/${id}`);
  }
};

// Workspace API Services
export const WorkspaceService = {
  /**
   * Get all workspaces for the current user
   */
  async getWorkspaces(token?: string): Promise<ApiResponse<Workspace[]>> {
    try {
      console.log("� WorkspaceService: Fetching workspaces from backend with deduplication...");
      const response = await api.get<Workspace[]>('/workspaces', {}, token);
      console.log("✅ WorkspaceService: Successfully fetched workspaces:", response.data?.length || 0);
      return response;
    } catch (error) {
      console.error("❌ WorkspaceService: Failed to fetch workspaces:", error);
      throw error;
    }
  },

  /**
   * Get a specific workspace by ID
   */
  async getWorkspace(id: string, includeMembers: boolean = false, token?: string): Promise<ApiResponse<Workspace>> {
    return api.get<Workspace>(`/workspaces/${id}?include_members=${includeMembers}`, {}, token);
  },

  /**
   * Create a new workspace
   */
  async createWorkspace(data: WorkspaceCreate, token?: string): Promise<ApiResponse<Workspace>> {
    return api.post<Workspace>('/workspaces', data, {}, token);
  },

  /**
   * Update an existing workspace
   */
  async updateWorkspace(id: string, data: WorkspaceUpdate, token?: string): Promise<ApiResponse<Workspace>> {
    return api.put<Workspace>(`/workspaces/${id}`, data, {}, token);
  },

  /**
   * Get all members in a workspace
   */
  async getWorkspaceMembers(id: string, token?: string): Promise<ApiResponse<WorkspaceMember[]>> {
    return api.get<WorkspaceMember[]>(`/workspaces/${id}/members`, {}, token);
  },

  /**
   * Add a member to a workspace
   */
  async addWorkspaceMember(workspaceId: string, member: { user_id: string; role_id: string }, token?: string): Promise<ApiResponse<WorkspaceMember>> {
    return api.post<WorkspaceMember>(`/workspaces/${workspaceId}/members`, member, {}, token);
  },

  /**
   * Update a workspace member's role
   */
  async updateWorkspaceMember(workspaceId: string, userId: string, update: { role_id?: string; status?: string }, token?: string): Promise<ApiResponse<WorkspaceMember>> {
    return api.put<WorkspaceMember>(`/workspaces/${workspaceId}/members/${userId}`, update, {}, token);
  },

  /**
   * Remove a member from a workspace
   */
  async removeWorkspaceMember(workspaceId: string, userId: string, token?: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/workspaces/${workspaceId}/members/${userId}`, {}, token);
  },

  /**
   * Delete a workspace
   */
  async deleteWorkspace(id: string, token?: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/workspaces/${id}`, {}, token);
  },

  /**
   * Invite a user to a workspace
   */
  async inviteUserToWorkspace(id: string, email: string, roleId: string, token?: string): Promise<ApiResponse<{ message: string }>> {
    return api.post<{ message: string }>(`/users/workspace/${id}/invite/?email=${email}&role_id=${roleId}`, {}, {}, token);
  }
};

// User API Services
export const UserService = {
  /**
   * Get the current user's information
   */
  async getCurrentUser(token?: string): Promise<ApiResponse<User>> {
    return api.get<User>('/users/me', {}, token);
  },

  /**
   * Update the current user's information
   */
  async updateCurrentUser(data: UserUpdate, token?: string): Promise<ApiResponse<User>> {
    return api.put<User>('/users/me', data, {}, token);
  }
};

// AI Analysis API Services
export const AIAnalysisService = {
  /**
   * Get the latest AI analysis for a contract
   */
  async getAnalysis(contractId: string): Promise<ApiResponse<AIAnalysisResult>> {
    return api.get<AIAnalysisResult>(`/contracts/${contractId}/analysis`);
  },

  /**
   * Get AI analysis history for a contract
   */
  async getAnalysisHistory(contractId: string, limit: number = 10): Promise<ApiResponse<AIAnalysisResult[]>> {
    return api.get<AIAnalysisResult[]>(`/contracts/${contractId}/analysis/history?limit=${limit}`);
  },

  /**
   * Run a new AI analysis on a contract
   */
  async runAnalysis(contractId: string, analysisType: string = 'contract_analysis'): Promise<ApiResponse<AIAnalysisResult>> {
    return api.post<AIAnalysisResult>(`/contracts/${contractId}/analyze?analysis_type=${analysisType}`, {});
  },

  /**
   * Apply AI suggestions to a contract (placeholder for future implementation)
   */
  async applySuggestions(contractId: string, suggestionIds: string[]): Promise<ApiResponse<{ message: string }>> {
    return api.post<{ message: string }>(`/contracts/${contractId}/analysis/apply-suggestions`, { suggestion_ids: suggestionIds });
  },

  /**
   * Export AI analysis as PDF (placeholder for future implementation)
   */
  async exportAnalysis(contractId: string): Promise<ApiResponse<{ url: string }>> {
    return api.get<{ url: string }>(`/contracts/${contractId}/analysis/export`);
  }
};

// Analytics API Services
export const AnalyticsService = {
  /**
   * Get dashboard summary data
   */
  async getDashboardSummary(workspaceId: string, token?: string): Promise<ApiResponse<DashboardSummary>> {
    return api.get<DashboardSummary>(`/analytics/dashboard?workspace_id=${workspaceId}`, {}, token);
  },

  /**
   * Get contract activity data
   */
  async getContractActivity(workspaceId: string, timeRange: string = '6months', token?: string): Promise<ApiResponse<ContractActivity[]>> {
    return api.get<ContractActivity[]>(`/analytics/contract-activity?workspace_id=${workspaceId}&time_range=${timeRange}`, {}, token);
  },

  /**
   * Get contract type distribution
   */
  async getContractTypeDistribution(workspaceId: string, token?: string): Promise<ApiResponse<ContractTypeDistribution[]>> {
    return api.get<ContractTypeDistribution[]>(`/analytics/contract-types?workspace_id=${workspaceId}`, {}, token);
  },

  /**
   * Get recent activities
   */
  async getRecentActivities(workspaceId: string, limit: number = 10, token?: string): Promise<ApiResponse<RecentActivity[]>> {
    return api.get<RecentActivity[]>(`/analytics/recent-activities?workspace_id=${workspaceId}&limit=${limit}`, {}, token);
  },

  /**
   * Get expiring contracts
   */
  async getExpiringContracts(workspaceId: string, days: number = 30, token?: string): Promise<ApiResponse<Contract[]>> {
    return api.get<Contract[]>(`/analytics/expiring-contracts?workspace_id=${workspaceId}&days=${days}`, {}, token);
  },

  /**
   * Get analytics summary
   */
  async getAnalyticsSummary(workspaceId: string, token?: string): Promise<ApiResponse<AnalyticsSummary>> {
    return api.get<AnalyticsSummary>(`/analytics/summary?workspace_id=${workspaceId}`, {}, token);
  },

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(workspaceId: string, token?: string): Promise<ApiResponse<PerformanceMetric[]>> {
    return api.get<PerformanceMetric[]>(`/analytics/performance-metrics?workspace_id=${workspaceId}`, {}, token);
  }
};

// Role API Services
export const RoleService = {
  /**
   * Get all roles for a workspace
   */
  async getWorkspaceRoles(workspaceId: string): Promise<ApiResponse<Role[]>> {
    return api.get<Role[]>(`/workspaces/${workspaceId}/roles`);
  },

  /**
   * Get a specific role by ID
   */
  async getRole(workspaceId: string, roleId: string): Promise<ApiResponse<Role>> {
    return api.get<Role>(`/workspaces/${workspaceId}/roles/${roleId}`);
  },

  /**
   * Create a new role
   */
  async createRole(data: RoleCreate): Promise<ApiResponse<Role>> {
    return api.post<Role>(`/workspaces/${data.workspace_id}/roles`, data);
  },

  /**
   * Update an existing role
   */
  async updateRole(workspaceId: string, roleId: string, data: RoleUpdate): Promise<ApiResponse<Role>> {
    return api.put<Role>(`/workspaces/${workspaceId}/roles/${roleId}`, data);
  },

  /**
   * Delete a role
   */
  async deleteRole(workspaceId: string, roleId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/workspaces/${workspaceId}/roles/${roleId}`);
  }
};

// Permission API Services
export const PermissionService = {
  /**
   * Get all permissions for a workspace
   */
  async getWorkspacePermissions(workspaceId: string): Promise<ApiResponse<Permission[]>> {
    return api.get<Permission[]>(`/workspaces/${workspaceId}/permissions`);
  }
};

// Document API Services
export const DocumentService = {
  /**
   * Get all documents with optional filtering
   */
  async getDocuments(params?: {
    workspace_id?: string;
    folder?: string;
    status?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }): Promise<ApiResponse<Document[]>> {
    // Build query string from params
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const queryString = queryParams.toString();
    const endpoint = queryString ? `/documents?${queryString}` : '/documents';

    return api.get<Document[]>(endpoint);
  },

  /**
   * Get a specific document by ID
   */
  async getDocument(id: string): Promise<ApiResponse<Document>> {
    return api.get<Document>(`/documents/${id}`);
  },

  /**
   * Create a new document
   */
  async createDocument(data: DocumentCreate): Promise<ApiResponse<Document>> {
    return api.post<Document>('/documents', data);
  },

  /**
   * Update an existing document
   */
  async updateDocument(id: string, data: DocumentUpdate): Promise<ApiResponse<Document>> {
    return api.put<Document>(`/documents/${id}`, data);
  },

  /**
   * Delete a document
   */
  async deleteDocument(id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/documents/${id}`);
  },

  /**
   * Upload a document file and create a document record
   */
  async uploadDocument(
    file: File,
    title: string,
    workspace_id: string,
    folder: string = "",
    content?: string
  ): Promise<ApiResponse<Document>> {
    // Create form data
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', title);
    formData.append('workspace_id', workspace_id);
    formData.append('folder', folder);
    if (content) {
      formData.append('content', content);
    }

    // Prepare headers
    const headers: Record<string, string> = {};

    // Add workspace ID header if available
    if (workspace_id) {
      headers["X-Workspace-ID"] = workspace_id;
    }

    // Add authentication token if available
    try {
      // Get the token from Clerk if it's available in the window object
      if (window.Clerk && window.Clerk.session) {
        const token = await window.Clerk.session.getToken();
        if (token) {
          headers["Authorization"] = `Bearer ${token}`;
        }
      }
    } catch (authError) {
      console.warn("Failed to get auth token:", authError);
      // Continue without the token
    }

    // Use fetch directly for multipart/form-data
    const response = await fetch('/api/documents/upload', {
      method: 'POST',
      body: formData,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw {
        status: response.status,
        message: errorData.detail || 'Failed to upload document',
        details: errorData,
        code: 'UPLOAD_ERROR',
      };
    }

    const data = await response.json();
    return {
      data,
      status: response.status,
      message: "Success"
    };
  },

  /**
   * Get a signed URL for a document file
   */
  async getDocumentFileUrl(id: string, expires_in: number = 3600): Promise<ApiResponse<{ url: string, expires_in: number, filename: string }>> {
    return api.get<{ url: string, expires_in: number, filename: string }>(`/documents/${id}/file?expires_in=${expires_in}`);
  },

  /**
   * Sign a document
   */
  async signDocument(id: string, signer_id: string, signature: { type: string, value: string }): Promise<ApiResponse<Document>> {
    return api.post<Document>(`/documents/${id}/sign`, {
      signer_id,
      signature
    });
  },

  /**
   * Decline to sign a document
   */
  async declineDocument(id: string, signer_id: string, reason: string): Promise<ApiResponse<Document>> {
    return api.post<Document>(`/documents/${id}/decline`, {
      signer_id,
      reason
    });
  }
};

// Storage API Services
export const StorageService = {
  /**
   * Upload a file to the storage bucket
   */
  async uploadFile(
    file: File,
    workspace_id: string,
    folder: string = ""
  ): Promise<ApiResponse<FileInfo>> {
    // Create form data
    const formData = new FormData();
    formData.append('file', file);
    formData.append('workspace_id', workspace_id);
    formData.append('folder', folder);

    // Prepare headers
    const headers: Record<string, string> = {};

    // Add workspace ID header if available
    if (workspace_id) {
      headers["X-Workspace-ID"] = workspace_id;
    }

    // Add authentication token if available
    try {
      // Get the token from Clerk if it's available in the window object
      if (window.Clerk && window.Clerk.session) {
        const token = await window.Clerk.session.getToken();
        if (token) {
          headers["Authorization"] = `Bearer ${token}`;
        }
      }
    } catch (authError) {
      console.warn("Failed to get auth token:", authError);
      // Continue without the token
    }

    // Use fetch directly for multipart/form-data
    const response = await fetch('/api/storage/upload', {
      method: 'POST',
      body: formData,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw {
        status: response.status,
        message: errorData.detail || 'Failed to upload file',
        details: errorData,
        code: 'UPLOAD_ERROR',
      };
    }

    const data = await response.json();
    return {
      data,
      status: response.status,
      message: "Success"
    };
  },

  /**
   * List files in a folder
   */
  async listFiles(workspace_id: string, folder: string = ""): Promise<ApiResponse<any[]>> {
    return api.get<any[]>(`/storage/files?workspace_id=${workspace_id}&folder=${folder}`);
  },

  /**
   * Get a signed URL for a file
   */
  async getFileUrl(file_path: string, expires_in: number = 3600): Promise<ApiResponse<{ url: string }>> {
    return api.get<{ url: string }>(`/storage/files/${file_path}?expires_in=${expires_in}`);
  },

  /**
   * Delete a file from the storage bucket
   */
  async deleteFile(file_path: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/storage/files/${file_path}`);
  }
};

// Folder API Services
export const FolderService = {
  /**
   * Get all folders for a workspace
   */
  async getFolders(workspace_id?: string): Promise<ApiResponse<any[]>> {
    const params = workspace_id ? `?workspace_id=${workspace_id}` : '';
    return api.get<any[]>(`/folders${params}`);
  },

  /**
   * Create a new folder
   */
  async createFolder(folderData: {
    name: string;
    description?: string;
    workspace_id: string;
    parent_id?: string;
  }): Promise<ApiResponse<any>> {
    return api.post<any>('/folders', folderData);
  },

  /**
   * Get a specific folder by ID
   */
  async getFolder(folder_id: string): Promise<ApiResponse<any>> {
    return api.get<any>(`/folders/${folder_id}`);
  },

  /**
   * Update a folder
   */
  async updateFolder(folder_id: string, folderData: {
    name?: string;
    description?: string;
    parent_id?: string;
  }): Promise<ApiResponse<any>> {
    return api.put<any>(`/folders/${folder_id}`, folderData);
  },

  /**
   * Delete a folder
   */
  async deleteFolder(folder_id: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/folders/${folder_id}`);
  }
};

// Workspace Member API Services
export const WorkspaceMemberService = {
  /**
   * Get all members of a workspace
   */
  async getWorkspaceMembers(workspaceId: string): Promise<ApiResponse<WorkspaceMember[]>> {
    return api.get<WorkspaceMember[]>(`/workspaces/${workspaceId}/members`);
  },

  /**
   * Update a member's role in a workspace
   */
  async updateMemberRole(workspaceId: string, userId: string, roleId: string): Promise<ApiResponse<WorkspaceMember>> {
    return api.put<WorkspaceMember>(`/workspaces/${workspaceId}/members/${userId}`, { role_id: roleId });
  },

  /**
   * Remove a member from a workspace
   */
  async removeMember(workspaceId: string, userId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/workspaces/${workspaceId}/members/${userId}`);
  },

  /**
   * Update a member's status in a workspace
   */
  async updateMemberStatus(workspaceId: string, userId: string, status: 'active' | 'inactive'): Promise<ApiResponse<WorkspaceMember>> {
    return api.put<WorkspaceMember>(`/workspaces/${workspaceId}/members/${userId}/status`, { status });
  }
};

// Notification API Services
export const NotificationService = {
  /**
   * Get notifications for the current user with filtering
   */
  async getNotifications(filters: NotificationFilters): Promise<ApiResponse<Notification[]>> {
    const params = new URLSearchParams();

    // Add required workspace_id
    params.append('workspace_id', filters.workspace_id);

    // Add optional filters
    if (filters.status) params.append('status', filters.status);
    if (filters.type) params.append('type', filters.type);
    if (filters.sender_id) params.append('sender_id', filters.sender_id);
    if (filters.entity_type) params.append('entity_type', filters.entity_type);
    if (filters.start_date) params.append('start_date', filters.start_date);
    if (filters.end_date) params.append('end_date', filters.end_date);
    if (filters.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters.limit !== undefined) params.append('limit', filters.limit.toString());

    return api.get<Notification[]>(`/notifications?${params.toString()}`);
  },

  /**
   * Get notification summary for the current user
   */
  async getNotificationSummary(workspaceId: string): Promise<ApiResponse<NotificationSummary>> {
    return api.get<NotificationSummary>(`/notifications/summary?workspace_id=${workspaceId}`);
  },

  /**
   * Get a specific notification by ID
   */
  async getNotification(notificationId: string): Promise<ApiResponse<Notification>> {
    return api.get<Notification>(`/notifications/${notificationId}`);
  },

  /**
   * Create a new notification
   */
  async createNotification(notificationData: NotificationCreate): Promise<ApiResponse<Notification>> {
    return api.post<Notification>('/notifications', notificationData);
  },

  /**
   * Update a notification
   */
  async updateNotification(notificationId: string, updateData: NotificationUpdate): Promise<ApiResponse<Notification>> {
    return api.patch<Notification>(`/notifications/${notificationId}`, updateData);
  },

  /**
   * Mark a notification as read
   */
  async markAsRead(notificationId: string): Promise<ApiResponse<Notification>> {
    return api.patch<Notification>(`/notifications/${notificationId}/read`, {});
  },

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(workspaceId: string): Promise<ApiResponse<{ message: string }>> {
    return api.patch<{ message: string }>(`/notifications/mark-all-read?workspace_id=${workspaceId}`, {});
  },

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/notifications/${notificationId}`);
  },

  /**
   * Delete all notifications
   */
  async deleteAllNotifications(workspaceId: string): Promise<ApiResponse<{ message: string }>> {
    return api.delete<{ message: string }>(`/notifications?workspace_id=${workspaceId}`);
  },

  /**
   * Bulk update notifications
   */
  async bulkUpdateNotifications(bulkUpdate: NotificationBulkUpdate): Promise<ApiResponse<{ message: string; updated_count: number; requested_count: number }>> {
    return api.patch<{ message: string; updated_count: number; requested_count: number }>('/notifications/bulk-update', bulkUpdate);
  },

  /**
   * Bulk delete notifications
   */
  async bulkDeleteNotifications(bulkDelete: NotificationBulkDelete): Promise<ApiResponse<{ message: string; deleted_count: number; requested_count: number }>> {
    return api.request<{ message: string; deleted_count: number; requested_count: number }>('/notifications/bulk-delete', {
      method: 'DELETE',
      body: JSON.stringify(bulkDelete)
    });
  }
};
