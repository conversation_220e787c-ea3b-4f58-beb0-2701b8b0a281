import { api } from './api-services';

// Types for contract lifecycle management
export interface ContractVersion {
  id: string;
  contract_id: string;
  version_number: number;
  title: string;
  content: any;
  changes_summary?: string;
  change_details?: any;
  created_by: {
    id: string;
    name: string;
  };
  created_at: string;
  is_current: boolean;
  workspace_id: string;
}

export interface ContractRenewal {
  id: string;
  contract_id: string;
  original_expiry_date: string;
  new_expiry_date: string;
  renewal_period?: string;
  auto_renewal: boolean;
  renewal_notice_period: number;
  renewal_status: 'pending' | 'approved' | 'rejected' | 'completed';
  renewal_terms?: any;
  requested_by: {
    id: string;
    name: string;
  };
  requested_at: string;
  approved_by?: {
    id: string;
    name: string;
  };
  approved_at?: string;
  completed_at?: string;
  workspace_id: string;
}

export interface ContractLifecycleEvent {
  id: string;
  contract_id: string;
  event_type: string;
  event_description: string;
  event_data?: any;
  triggered_by?: string;
  triggered_at: string;
  workspace_id: string;
}

export interface ContractReminder {
  id: string;
  contract_id: string;
  reminder_type: string;
  reminder_date: string;
  days_before_event?: number;
  message: string;
  recipients: string[];
  sent: boolean;
  sent_at?: string;
  workspace_id: string;
}

export interface ContractLifecycleStats {
  total_contracts: number;
  active_contracts: number;
  expired_contracts: number;
  pending_renewals: number;
  upcoming_expirations: number;
  average_contract_duration?: number;
  renewal_rate?: number;
}

export interface ExpirationAlert {
  contract_id: string;
  contract_title: string;
  expiry_date: string;
  days_until_expiry: number;
  status: string;
  workspace_id: string;
}

export interface VersionComparison {
  from_version: ContractVersion;
  to_version: ContractVersion;
  differences: Array<{
    field: string;
    old_value: any;
    new_value: any;
    change_type: string;
  }>;
  summary: string;
}

export interface BulkRenewalRequest {
  contract_ids: string[];
  new_expiry_date: string;
  renewal_period?: string;
  renewal_terms?: any;
}

export interface BulkRenewalResponse {
  successful_renewals: string[];
  failed_renewals: Array<{
    contract_id: string;
    error: string;
  }>;
  total_processed: number;
}

export interface StatusTransitionRequest {
  contract_id: string;
  new_status: string;
  reason?: string;
  effective_date?: string;
}

// Contract Lifecycle Service
export const ContractLifecycleService = {
  // Version Management
  async getContractVersions(contractId: string, token?: string) {
    return api.get<ContractVersion[]>(`/lifecycle/contracts/${contractId}/versions`, {}, token);
  },

  async getCurrentVersion(contractId: string, token?: string) {
    return api.get<ContractVersion>(`/lifecycle/contracts/${contractId}/versions/current`, {}, token);
  },

  async createContractVersion(contractId: string, versionData?: any, token?: string) {
    return api.post<ContractVersion>(`/lifecycle/contracts/${contractId}/versions`, versionData, {}, token);
  },

  async compareVersions(version1Id: string, version2Id: string, token?: string) {
    return api.get<VersionComparison>(`/lifecycle/versions/${version1Id}/compare/${version2Id}`, {}, token);
  },

  // Renewal Management
  async getContractRenewals(contractId: string, token?: string) {
    return api.get<ContractRenewal[]>(`/lifecycle/contracts/${contractId}/renewals`, {}, token);
  },

  async createContractRenewal(contractId: string, renewalData?: any, token?: string) {
    return api.post<ContractRenewal>(`/lifecycle/contracts/${contractId}/renewals`, renewalData, {}, token);
  },

  async approveRenewal(renewalId: string, token?: string) {
    return api.put<ContractRenewal>(`/lifecycle/renewals/${renewalId}/approve`, {}, {}, token);
  },

  async completeRenewal(renewalId: string, token?: string) {
    return api.put<ContractRenewal>(`/lifecycle/renewals/${renewalId}/complete`, {}, {}, token);
  },

  // Lifecycle Events
  async getLifecycleEvents(contractId: string, token?: string) {
    return api.get<ContractLifecycleEvent[]>(`/lifecycle/contracts/${contractId}/events`, {}, token);
  },

  async createLifecycleEvent(contractId: string, eventData: any, token?: string) {
    return api.post<ContractLifecycleEvent>(`/lifecycle/contracts/${contractId}/events`, eventData, {}, token);
  },

  // Reminders
  async getContractReminders(contractId: string, token?: string) {
    return api.get<ContractReminder[]>(`/lifecycle/contracts/${contractId}/reminders`, {}, token);
  },

  async getPendingReminders(workspaceId: string, token?: string) {
    return api.get<ContractReminder[]>(`/lifecycle/workspaces/${workspaceId}/reminders/pending`, {}, token);
  },

  async markReminderSent(reminderId: string, token?: string) {
    return api.put<ContractReminder>(`/lifecycle/reminders/${reminderId}/mark-sent`, {}, {}, token);
  },

  // Analytics and Reporting
  async getLifecycleStats(workspaceId: string, token?: string) {
    return api.get<ContractLifecycleStats>(`/lifecycle/workspaces/${workspaceId}/lifecycle/stats`, {}, token);
  },

  async getExpirationAlerts(workspaceId: string, daysAhead: number = 30, token?: string) {
    return api.get<ExpirationAlert[]>(`/lifecycle/workspaces/${workspaceId}/expiration-alerts?days_ahead=${daysAhead}`, {}, token);
  },

  // Bulk Operations
  async bulkRenewal(workspaceId: string, bulkRequest: BulkRenewalRequest, token?: string) {
    return api.post<BulkRenewalResponse>(`/lifecycle/workspaces/${workspaceId}/bulk-renewal`, bulkRequest, {}, token);
  },

  // Status Management
  async transitionContractStatus(contractId: string, statusRequest: StatusTransitionRequest, token?: string) {
    return api.put<any>(`/lifecycle/contracts/${contractId}/status`, statusRequest, {}, token);
  },

  // Automated Operations
  async updateExpiredContracts(workspaceId: string, token?: string) {
    return api.post<{ expired_contracts: number }>(`/lifecycle/workspaces/${workspaceId}/update-expired`, {}, {}, token);
  },
};

// Helper functions for frontend components
export const LifecycleHelpers = {
  formatDate: (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  },

  getUserInitials: (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  },

  getDaysUntilExpiry: (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  getExpiryStatus: (expiryDate: string) => {
    const daysUntil = LifecycleHelpers.getDaysUntilExpiry(expiryDate);
    
    if (daysUntil < 0) return { status: 'expired', color: 'red' };
    if (daysUntil <= 7) return { status: 'critical', color: 'red' };
    if (daysUntil <= 30) return { status: 'warning', color: 'yellow' };
    return { status: 'normal', color: 'green' };
  },

  parseChanges: (summary?: string, changeDetails?: any) => {
    if (changeDetails && Array.isArray(changeDetails)) {
      return changeDetails;
    }
    if (summary) {
      return [summary];
    }
    return ["No changes recorded"];
  },

  getRenewalStatusColor: (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'approved': return 'blue';
      case 'rejected': return 'red';
      case 'pending': return 'yellow';
      default: return 'gray';
    }
  },

  getEventTypeIcon: (eventType: string) => {
    switch (eventType) {
      case 'created': return '📄';
      case 'updated': return '✏️';
      case 'status_changed': return '🔄';
      case 'approved': return '✅';
      case 'activated': return '🟢';
      case 'renewed': return '🔄';
      case 'expired': return '⏰';
      case 'terminated': return '❌';
      default: return '📋';
    }
  }
};
