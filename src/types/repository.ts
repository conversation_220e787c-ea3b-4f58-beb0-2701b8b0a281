// Shared types for repository components

export interface Folder {
  id: string;
  name: string;
  parentId: string | null;
  workspaceId: string;
  createdBy: string;
  createdDate: string;
  documentCount?: number;
}

export interface CustomFolder {
  id: string;
  name: string;
  type: 'system' | 'custom';
  templateCount: number;
  icon: string;
}

export interface DocumentUploadResult {
  id: string;
  title: string;
  type: string;
  content: string;
  folderId: string | null;
  createdDate: string;
}
