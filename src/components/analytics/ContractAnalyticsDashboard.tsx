import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { 
  FileText, 
  TrendingUp, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  Calendar,
  DollarSign,
  Users,
  RefreshCw,
  Loader2,
  Target,
  Award,
  Zap
} from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { ContractLifecycleService } from "@/services/contract-lifecycle-service";
import { toast } from "sonner";

interface ContractAnalytics {
  total_contracts: number;
  active_contracts: number;
  expired_contracts: number;
  pending_renewals: number;
  upcoming_expirations: number;
  average_contract_duration?: number;
  renewal_rate?: number;
  contract_types: Record<string, number>;
  status_distribution: Record<string, number>;
  monthly_trends: Array<{
    month: string;
    created: number;
    expired: number;
    renewed: number;
  }>;
  value_metrics: {
    total_value: number;
    average_value: number;
    largest_contract: number;
  };
  compliance_score: number;
  risk_assessment: {
    high_risk: number;
    medium_risk: number;
    low_risk: number;
  };
}

interface ContractAnalyticsDashboardProps {
  workspaceId: string;
}

const ContractAnalyticsDashboard = ({ workspaceId }: ContractAnalyticsDashboardProps) => {
  const { getToken } = useAuth();
  const [analytics, setAnalytics] = useState<ContractAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState<number>(30);
  const [refreshing, setRefreshing] = useState(false);

  // Color palette for charts
  const colors = [
    "#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#00ff00", 
    "#ff00ff", "#00ffff", "#ff0000", "#0000ff", "#ffff00"
  ];

  // Load analytics data
  const loadAnalytics = async (days: number = period) => {
    try {
      setLoading(true);
      const token = await getToken();

      // Get lifecycle stats
      const lifecycleStats = await ContractLifecycleService.getLifecycleStats(workspaceId, token);
      
      // Get expiration alerts
      const expirationAlerts = await ContractLifecycleService.getExpirationAlerts(workspaceId, 30, token);

      // Mock additional analytics data (in real implementation, these would come from API)
      const mockAnalytics: ContractAnalytics = {
        ...lifecycleStats,
        contract_types: {
          "Service Agreement": 45,
          "NDA": 32,
          "Employment": 28,
          "Vendor": 23,
          "Partnership": 15,
          "License": 12
        },
        status_distribution: {
          "Active": lifecycleStats.active_contracts,
          "Draft": Math.floor(lifecycleStats.total_contracts * 0.15),
          "Review": Math.floor(lifecycleStats.total_contracts * 0.08),
          "Expired": lifecycleStats.expired_contracts,
          "Terminated": Math.floor(lifecycleStats.total_contracts * 0.05)
        },
        monthly_trends: [
          { month: "Jan", created: 12, expired: 3, renewed: 8 },
          { month: "Feb", created: 15, expired: 2, renewed: 6 },
          { month: "Mar", created: 18, expired: 4, renewed: 9 },
          { month: "Apr", created: 22, expired: 1, renewed: 12 },
          { month: "May", created: 19, expired: 3, renewed: 10 },
          { month: "Jun", created: 25, expired: 2, renewed: 14 }
        ],
        value_metrics: {
          total_value: 2450000,
          average_value: 125000,
          largest_contract: 850000
        },
        compliance_score: 87,
        risk_assessment: {
          high_risk: Math.floor(lifecycleStats.total_contracts * 0.12),
          medium_risk: Math.floor(lifecycleStats.total_contracts * 0.25),
          low_risk: Math.floor(lifecycleStats.total_contracts * 0.63)
        }
      };

      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Analytics error:', error);
      toast.error('Failed to load contract analytics');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh analytics
  const refreshAnalytics = async () => {
    setRefreshing(true);
    await loadAnalytics();
  };

  // Prepare chart data
  const getContractTypesData = () => {
    if (!analytics?.contract_types) return [];
    
    return Object.entries(analytics.contract_types).map(([type, count]) => ({
      name: type,
      value: count,
      percentage: Math.round((count / analytics.total_contracts) * 100)
    }));
  };

  const getStatusDistributionData = () => {
    if (!analytics?.status_distribution) return [];
    
    return Object.entries(analytics.status_distribution).map(([status, count]) => ({
      name: status,
      value: count,
      percentage: Math.round((count / analytics.total_contracts) * 100)
    }));
  };

  const getRiskAssessmentData = () => {
    if (!analytics?.risk_assessment) return [];
    
    return [
      { name: "Low Risk", value: analytics.risk_assessment.low_risk, color: "#22c55e" },
      { name: "Medium Risk", value: analytics.risk_assessment.medium_risk, color: "#f59e0b" },
      { name: "High Risk", value: analytics.risk_assessment.high_risk, color: "#ef4444" }
    ];
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Calculate summary stats
  const getSummaryStats = () => {
    if (!analytics) return null;

    const renewalRate = analytics.renewal_rate || 0;
    const avgDuration = analytics.average_contract_duration || 0;
    const complianceScore = analytics.compliance_score || 0;

    return {
      totalContracts: analytics.total_contracts,
      activeContracts: analytics.active_contracts,
      renewalRate: Math.round(renewalRate * 100),
      avgDuration: Math.round(avgDuration),
      complianceScore,
      totalValue: analytics.value_metrics.total_value,
      upcomingExpirations: analytics.upcoming_expirations
    };
  };

  useEffect(() => {
    if (workspaceId) {
      loadAnalytics();
    }
  }, [workspaceId, period]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading contract analytics...</span>
      </div>
    );
  }

  const summaryStats = getSummaryStats();
  const contractTypesData = getContractTypesData();
  const statusDistributionData = getStatusDistributionData();
  const riskAssessmentData = getRiskAssessmentData();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Contract Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into your contract portfolio
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={period.toString()} onValueChange={(value) => setPeriod(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon"
            onClick={refreshAnalytics}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Contracts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.totalContracts}</div>
              <p className="text-xs text-muted-foreground">
                {summaryStats.activeContracts} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summaryStats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Total contract value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Renewal Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.renewalRate}%</div>
              <Progress value={summaryStats.renewalRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.complianceScore}%</div>
              <Progress value={summaryStats.complianceScore} className="mt-2" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contract Types Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Contract Types</CardTitle>
            <CardDescription>
              Distribution by contract type
            </CardDescription>
          </CardHeader>
          <CardContent>
            {contractTypesData.length > 0 ? (
              <div className="space-y-4">
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={contractTypesData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {contractTypesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="space-y-2">
                  {contractTypesData.slice(0, 5).map((item, index) => (
                    <div key={item.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: colors[index % colors.length] }}
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{item.value}</span>
                        <Badge variant="secondary" className="text-xs">
                          {item.percentage}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Trends</CardTitle>
            <CardDescription>
              Contract activity over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics?.monthly_trends && analytics.monthly_trends.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analytics.monthly_trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="created" 
                    stackId="1"
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    fillOpacity={0.6}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="renewed" 
                    stackId="1"
                    stroke="#82ca9d" 
                    fill="#82ca9d" 
                    fillOpacity={0.6}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="expired" 
                    stackId="1"
                    stroke="#ffc658" 
                    fill="#ffc658" 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No trend data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Risk Assessment and Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Risk Assessment */}
        <Card>
          <CardHeader>
            <CardTitle>Risk Assessment</CardTitle>
            <CardDescription>
              Contract risk distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            {riskAssessmentData.length > 0 ? (
              <div className="space-y-4">
                {riskAssessmentData.map((item, index) => (
                  <div key={item.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.name}</span>
                      <span className="text-sm">{item.value} contracts</span>
                    </div>
                    <Progress 
                      value={(item.value / analytics!.total_contracts) * 100} 
                      className="h-2"
                      style={{ 
                        '--progress-background': item.color 
                      } as React.CSSProperties}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No risk data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Status Distribution</CardTitle>
            <CardDescription>
              Contracts by current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statusDistributionData.length > 0 ? (
              <div className="space-y-3">
                {statusDistributionData.map((item, index) => (
                  <div key={item.name} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: colors[index % colors.length] }}
                      />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold">{item.value}</span>
                      <Badge variant="outline">
                        {item.percentage}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                No status data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* AI Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            AI-Powered Insights
          </CardTitle>
          <CardDescription>
            Intelligent recommendations based on your contract portfolio
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {summaryStats && (
              <>
                {summaryStats.upcomingExpirations > 0 && (
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border-l-4 border-yellow-400">
                    <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Expiration Alert</h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-200 mt-1">
                      {summaryStats.upcomingExpirations} contracts are expiring in the next 30 days. 
                      Consider initiating renewal processes to avoid service disruptions.
                    </p>
                  </div>
                )}
                
                {summaryStats.renewalRate < 70 && (
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border-l-4 border-orange-400">
                    <h4 className="font-medium text-orange-900 dark:text-orange-100">Renewal Optimization</h4>
                    <p className="text-sm text-orange-700 dark:text-orange-200 mt-1">
                      Your renewal rate of {summaryStats.renewalRate}% could be improved. 
                      Consider implementing automated renewal reminders and better contract terms.
                    </p>
                  </div>
                )}
                
                {summaryStats.complianceScore > 85 && (
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-400">
                    <h4 className="font-medium text-green-900 dark:text-green-100">Excellent Compliance</h4>
                    <p className="text-sm text-green-700 dark:text-green-200 mt-1">
                      Your compliance score of {summaryStats.complianceScore}% is excellent! 
                      Keep up the good work with contract management best practices.
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContractAnalyticsDashboard;
