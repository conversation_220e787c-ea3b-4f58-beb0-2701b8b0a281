import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "../ui/button";
import { ArrowLeft } from "lucide-react";
import EnhancedAIAnalysis from "./EnhancedAIAnalysis";

interface ContractAIAnalysisDashboardProps {
  contractId?: string;
  contractName?: string;
  isFetching?: boolean;
}

const ContractAIAnalysisDashboard: React.FC<ContractAIAnalysisDashboardProps> = ({
  contractId: propContractId,
  contractName,
  isFetching = false,
}) => {
  const { contractId: urlContractId } = useParams<{ contractId: string }>();
  const navigate = useNavigate();
  
  // Use prop contractId or URL parameter
  const contractId = propContractId || urlContractId;

  const handleGoBack = () => {
    if (contractId) {
      navigate(`/app/contracts/${contractId}`);
    } else {
      navigate('/app/contracts');
    }
  };

  if (!contractId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">No contract ID provided</p>
          <Button onClick={handleGoBack} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Contract
        </Button>
        {contractName && (
          <div>
            <h1 className="text-2xl font-bold">AI Analysis</h1>
            <p className="text-muted-foreground">{contractName}</p>
          </div>
        )}
      </div>

      {/* Enhanced AI Analysis Component */}
      <EnhancedAIAnalysis 
        contractId={contractId} 
        contractTitle={contractName || "Contract"}
      />
    </div>
  );
};

export default ContractAIAnalysisDashboard;
